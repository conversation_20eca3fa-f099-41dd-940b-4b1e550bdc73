import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { User, Session } from '@supabase/supabase-js'
import { useToast } from '@/hooks/use-toast'

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    const supabase = createClient()

    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const result = await response.json()

      if (!response.ok) {
        toast({
          title: "Erro no login",
          description: result.error,
          variant: "destructive"
        })
        return { error: result.error }
      }

      return { error: null }
    } catch (error: any) {
      toast({
        title: "Erro no login",
        description: "Erro inesperado ao fazer login",
        variant: "destructive"
      })
      return { error }
    }
  }

  const signUp = async (email: string, password: string, name: string) => {
    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, name }),
      })

      const result = await response.json()

      if (!response.ok) {
        toast({
          title: "Erro no cadastro",
          description: result.error,
          variant: "destructive"
        })
        return { error: result.error }
      }

      toast({
        title: "Cadastro realizado",
        description: result.message || "Verifique seu email para confirmar a conta"
      })

      return { error: null }
    } catch (error: any) {
      toast({
        title: "Erro no cadastro",
        description: "Erro inesperado ao fazer cadastro",
        variant: "destructive"
      })
      return { error }
    }
  }

  const signOut = async () => {
    try {
      await fetch('/api/auth/signout', {
        method: 'POST',
      })

      toast({
        title: "Logout realizado",
        description: "Você foi desconectado com sucesso"
      })
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut
  }
}
