import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type PatientAssociation = Tables<'patient_healthcare_professional_associations'>
type PatientAssociationInsert = TablesInsert<'patient_healthcare_professional_associations'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const patientId = searchParams.get('patient_id')
      const healthcareProfessionalId = searchParams.get('healthcare_professional_id')

      let query = supabase
        .from('patient_healthcare_professional_associations')
        .select(`
          *,
          patients(name),
          healthcare_professionals(name, specialty)
        `)

      if (patientId) {
        query = query.eq('patient_id', patientId)
      }

      if (healthcareProfessionalId) {
        query = query.eq('healthcare_professional_id', healthcareProfessionalId)
      }

      const { data: associations, error } = await query.order('created_at', { ascending: false })

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(associations || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      const { patient_id, healthcare_professional_id } = body

      if (!patient_id || !healthcare_professional_id) {
        return createApiResponse(null, 'patient_id and healthcare_professional_id are required', 400)
      }

      // Validate that the user has access to the patient
      const { data: accessiblePatients, error: patientsError } = await supabase
        .rpc('get_accessible_patients', { current_user_id: userId })

      if (patientsError) {
        return handleApiError(patientsError)
      }

      const hasPatientAccess = accessiblePatients?.some((p: any) => p.id === patient_id)
      if (!hasPatientAccess) {
        return createApiResponse(null, 'Patient not found or access denied', 404)
      }

      // Validate that the healthcare professional exists and is accessible
      const { data: accessibleUsers, error: usersError } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      if (usersError) {
        return handleApiError(usersError)
      }

      const accessibleUserIds = accessibleUsers?.map((user: any) => user.user_id) || []

      const { data: healthcareProfessional, error: hpError } = await supabase
        .from('healthcare_professionals')
        .select('id, user_id')
        .eq('id', healthcare_professional_id)
        .single()

      if (hpError || !healthcareProfessional || !accessibleUserIds.includes(healthcareProfessional.user_id)) {
        return createApiResponse(null, 'Healthcare professional not found or access denied', 404)
      }

      const associationData: PatientAssociationInsert = {
        patient_id,
        healthcare_professional_id,
        created_by: userId
      }

      const { data: association, error } = await supabase
        .from('patient_healthcare_professional_associations')
        .insert(associationData)
        .select(`
          *,
          patients(name),
          healthcare_professionals(name, specialty)
        `)
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(association, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function DELETE(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const associationId = searchParams.get('id')

      if (!associationId) {
        return createApiResponse(null, 'Association ID is required', 400)
      }

      const { error } = await supabase
        .from('patient_healthcare_professional_associations')
        .delete()
        .eq('id', associationId)
        .eq('created_by', userId)

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse({ success: true })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
