"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { debugLog } from '@/lib/debug-utils';

interface SelectedProfessionalContextType {
  selectedProfessional: string;
  setSelectedProfessional: (id: string) => void;
}

const SelectedProfessionalContext = createContext<SelectedProfessionalContextType | undefined>(undefined);

interface SelectedProfessionalProviderProps {
  children: ReactNode;
}

const STORAGE_KEY = 'nectar-selected-professional';

export function SelectedProfessionalProvider({ children }: SelectedProfessionalProviderProps) {
  const [selectedProfessional, setSelectedProfessionalState] = useState<string>('current');

  // Carregar do localStorage na inicialização
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        setSelectedProfessionalState(stored);
      }
    } catch (error) {
      console.error('Error loading selected professional from localStorage:', error);
    }
  }, []);

  // Salvar no localStorage quando mudar
  const setSelectedProfessional = (id: string) => {
    try {
      localStorage.setItem(STORAGE_KEY, id);
      setSelectedProfessionalState(id);
      debugLog.info('🔄 Selected professional changed:', id);
    } catch (error) {
      console.error('Error saving selected professional to localStorage:', error);
      setSelectedProfessionalState(id);
    }
  };

  return (
    <SelectedProfessionalContext.Provider value={{
      selectedProfessional,
      setSelectedProfessional
    }}>
      {children}
    </SelectedProfessionalContext.Provider>
  );
}

export function useSelectedProfessional() {
  const context = useContext(SelectedProfessionalContext);
  if (context === undefined) {
    throw new Error('useSelectedProfessional must be used within a SelectedProfessionalProvider');
  }
  return context;
}
