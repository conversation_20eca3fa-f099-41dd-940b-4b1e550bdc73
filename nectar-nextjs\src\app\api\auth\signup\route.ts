import { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createApiResponse, handleApiError } from '@/lib/api-utils'

export async function POST(request: NextRequest) {
  try {
    const { email, password, name } = await request.json()

    if (!email || !password || !name) {
      return createApiResponse(undefined, 'Email, password, and name are required', 400)
    }

    const supabase = await createClient()

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: name
        }
      }
    })

    if (error) {
      return createApiResponse(undefined, error.message, 400)
    }

    return createApiResponse({
      user: data.user,
      session: data.session,
      message: 'Please check your email to confirm your account'
    })
  } catch (error) {
    return handleApiError(error)
  }
}
