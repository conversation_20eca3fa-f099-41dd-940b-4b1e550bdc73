import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarIcon, Clock } from 'lucide-react';
import { format, parse, isValid } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { toLocalISOString } from '@/lib/date-utils';

interface DateTimeInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  id?: string;
}

export function DateTimeInput({
  value = '',
  onChange,
  placeholder = 'DD/MM/AAAA HH:mm',
  disabled = false,
  className,
  id
}: DateTimeInputProps) {
  const [open, setOpen] = useState(false);
  const [dateValue, setDateValue] = useState<Date | undefined>();
  const [timeValue, setTimeValue] = useState('');
  const [inputValue, setInputValue] = useState('');

  // Parse the initial value
  useEffect(() => {
    if (value) {
      try {
        const date = new Date(value);
        if (isValid(date)) {
          setDateValue(date);
          setTimeValue(format(date, 'HH:mm'));
          setInputValue(format(date, 'dd/MM/yyyy HH:mm', { locale: ptBR }));
        }
      } catch (error) {
        console.error('Error parsing date:', error);
      }
    } else {
      setDateValue(undefined);
      setTimeValue('');
      setInputValue('');
    }
  }, [value]);

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setDateValue(date);

      // Combine date with existing time or default to current time
      const timeToUse = timeValue || format(new Date(), 'HH:mm');
      const [hours, minutes] = timeToUse.split(':').map(Number);

      const newDate = new Date(date);
      newDate.setHours(hours, minutes, 0, 0);

      const formattedDisplay = format(newDate, 'dd/MM/yyyy HH:mm', { locale: ptBR });
      const isoValue = toLocalISOString(newDate);

      setInputValue(formattedDisplay);
      setTimeValue(timeToUse);
      onChange?.(isoValue);
    }
    setOpen(false);
  };

  const handleTimeChange = (time: string) => {
    setTimeValue(time);

    if (dateValue && time) {
      const [hours, minutes] = time.split(':').map(Number);
      const newDate = new Date(dateValue);
      newDate.setHours(hours, minutes, 0, 0);

      const formattedDisplay = format(newDate, 'dd/MM/yyyy HH:mm', { locale: ptBR });
      const isoValue = toLocalISOString(newDate);

      setInputValue(formattedDisplay);
      onChange?.(isoValue);
    }
  };

  const handleInputChange = (inputText: string) => {
    setInputValue(inputText);
    
    // Try to parse the input in Brazilian format
    try {
      // Support formats: DD/MM/YYYY HH:mm, DD/MM/YYYY HH:mm:ss, DD/MM/YYYY
      let parsedDate: Date | null = null;
      
      if (inputText.match(/^\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}$/)) {
        parsedDate = parse(inputText, 'dd/MM/yyyy HH:mm', new Date(), { locale: ptBR });
      } else if (inputText.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
        parsedDate = parse(inputText + ' 09:00', 'dd/MM/yyyy HH:mm', new Date(), { locale: ptBR });
      }
      
      if (parsedDate && isValid(parsedDate)) {
        setDateValue(parsedDate);
        setTimeValue(format(parsedDate, 'HH:mm'));
        const isoValue = toLocalISOString(parsedDate);
        onChange?.(isoValue);
      }
    } catch (error) {
      // Invalid input, just update the display value
    }
  };

  const handleInputBlur = () => {
    // If input is invalid, reset to the last valid value
    if (dateValue) {
      const formattedDisplay = format(dateValue, 'dd/MM/yyyy HH:mm', { locale: ptBR });
      setInputValue(formattedDisplay);
    }
  };

  return (
    <div className="flex gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "justify-start text-left font-normal flex-1",
              !dateValue && "text-muted-foreground",
              className
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            <Input
              id={id}
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              onBlur={handleInputBlur}
              placeholder={placeholder}
              className="border-0 p-0 h-auto bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
              disabled={disabled}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            locale={ptBR}
            selected={dateValue}
            onSelect={handleDateSelect}
            initialFocus
          />
          <div className="p-3 border-t">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <Input
                type="time"
                value={timeValue}
                onChange={(e) => handleTimeChange(e.target.value)}
                className="w-auto"
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
