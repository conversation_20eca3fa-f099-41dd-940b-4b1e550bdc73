import React, { useState, useEffect, useRef } from 'react'
import { Search, Plus, User, Phone, Stethoscope } from 'lucide-react'
import { Input } from './input'
import { Button } from './button'
import { Card, CardContent } from './card'
import { Badge } from './badge'
import { cn } from '@/lib/utils'

interface Patient {
  id: string
  name: string
  phone?: string
  email?: string
}

interface HealthcareProfessional {
  id: string
  name: string
  phone?: string
  specialty?: string
}

interface SmartSearchProps {
  placeholder: string
  value?: string
  onSelect: (id: string) => void
  onAddNew?: () => void
  className?: string
  disabled?: boolean
}

interface PatientSearchProps extends SmartSearchProps {
  patients: Patient[]
  selectedPatientId?: string
}

interface ProfessionalSearchProps extends SmartSearchProps {
  professionals: HealthcareProfessional[]
  selectedProfessionalId?: string
}

// Patient Search Component
export function PatientSearch({
  patients,
  selectedPatientId,
  placeholder = "Buscar paciente por nome ou telefone...",
  onSelect,
  onAddNew,
  className,
  disabled = false
}: PatientSearchProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([])
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Get selected patient for display
  const selectedPatient = patients.find(p => p.id === selectedPatientId)

  // Filter patients based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredPatients(patients.slice(0, 10)) // Show first 10 when no search
      return
    }

    const filtered = patients.filter(patient => {
      const searchLower = searchTerm.toLowerCase()
      const nameMatch = patient.name.toLowerCase().includes(searchLower)
      
      // Fix phone filtering - only match if search has digits AND phone contains those digits
      const cleanSearchPhone = searchTerm.replace(/\D/g, '')
      const cleanPatientPhone = patient.phone?.replace(/\D/g, '') || ''
      const phoneMatch = cleanSearchPhone.length > 0 && cleanPatientPhone.includes(cleanSearchPhone)
      
      const emailMatch = patient.email?.toLowerCase().includes(searchLower)
      
      return nameMatch || phoneMatch || emailMatch
    }).slice(0, 10) // Limit to 10 results

    setFilteredPatients(filtered)
  }, [searchTerm, patients])

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleSelect = (patient: Patient) => {
    onSelect(patient.id)
    setSearchTerm('')
    setIsOpen(false)
  }

  const handleInputFocus = () => {
    setIsOpen(true)
    if (selectedPatient) {
      setSearchTerm(selectedPatient.name)
    }
  }

  const handleInputChange = (value: string) => {
    setSearchTerm(value)
    setIsOpen(true)
  }

  return (
    <div ref={searchRef} className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={selectedPatient ? selectedPatient.name : placeholder}
          value={searchTerm}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={handleInputFocus}
          className="pl-10"
          disabled={disabled}
        />
      </div>

      {isOpen && (
        <Card className="absolute z-50 w-full mt-1 max-h-80 overflow-y-auto">
          <CardContent className="p-2">
            {onAddNew && (
              <Button
                variant="ghost"
                className="w-full justify-start mb-2 h-auto p-3"
                onClick={() => {
                  onAddNew()
                  setIsOpen(false)
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                <span>Adicionar novo paciente</span>
              </Button>
            )}

            {filteredPatients.length === 0 ? (
              <div className="p-3 text-center text-muted-foreground">
                <User className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Nenhum paciente encontrado</p>
              </div>
            ) : (
              <div className="space-y-1">
                {filteredPatients.map((patient) => (
                  <Button
                    key={patient.id}
                    variant="ghost"
                    className="w-full justify-start h-auto p-3"
                    onClick={() => handleSelect(patient)}
                  >
                    <div className="flex items-center space-x-3 w-full">
                      <User className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <div className="flex-1 text-left">
                        <div className="font-medium">{patient.name}</div>
                        <div className="text-sm text-muted-foreground flex items-center space-x-2">
                          {patient.phone && (
                            <span className="flex items-center">
                              <Phone className="h-3 w-3 mr-1" />
                              {patient.phone}
                            </span>
                          )}
                          {patient.email && (
                            <span className="truncate">{patient.email}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Professional Search Component
export function ProfessionalSearch({
  professionals,
  selectedProfessionalId,
  placeholder = "Buscar profissional por nome ou telefone...",
  onSelect,
  onAddNew,
  className,
  disabled = false
}: ProfessionalSearchProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [filteredProfessionals, setFilteredProfessionals] = useState<HealthcareProfessional[]>([])
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Get selected professional for display
  const selectedProfessional = professionals.find(p => p.id === selectedProfessionalId)

  // Filter professionals based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredProfessionals(professionals.slice(0, 10)) // Show first 10 when no search
      return
    }

    const filtered = professionals.filter(professional => {
      const searchLower = searchTerm.toLowerCase()
      const nameMatch = professional.name.toLowerCase().includes(searchLower)
      
      // Fix phone filtering - only match if search has digits AND phone contains those digits
      const cleanSearchPhone = searchTerm.replace(/\D/g, '')
      const cleanProfessionalPhone = professional.phone?.replace(/\D/g, '') || ''
      const phoneMatch = cleanSearchPhone.length > 0 && cleanProfessionalPhone.includes(cleanSearchPhone)
      
      const specialtyMatch = professional.specialty?.toLowerCase().includes(searchLower)
      
      return nameMatch || phoneMatch || specialtyMatch
    }).slice(0, 10) // Limit to 10 results

    setFilteredProfessionals(filtered)
  }, [searchTerm, professionals])

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleSelect = (professional: HealthcareProfessional) => {
    onSelect(professional.id)
    setSearchTerm('')
    setIsOpen(false)
  }

  const handleInputFocus = () => {
    setIsOpen(true)
    if (selectedProfessional) {
      setSearchTerm(selectedProfessional.name)
    }
  }

  const handleInputChange = (value: string) => {
    setSearchTerm(value)
    setIsOpen(true)
  }

  return (
    <div ref={searchRef} className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={selectedProfessional ? selectedProfessional.name : placeholder}
          value={searchTerm}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={handleInputFocus}
          className="pl-10"
          disabled={disabled}
        />
      </div>

      {isOpen && (
        <Card className="absolute z-50 w-full mt-1 max-h-80 overflow-y-auto">
          <CardContent className="p-2">
            {onAddNew && (
              <Button
                variant="ghost"
                className="w-full justify-start mb-2 h-auto p-3"
                onClick={() => {
                  onAddNew()
                  setIsOpen(false)
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                <span>Adicionar novo profissional</span>
              </Button>
            )}

            {filteredProfessionals.length === 0 ? (
              <div className="p-3 text-center text-muted-foreground">
                <Stethoscope className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Nenhum profissional encontrado</p>
              </div>
            ) : (
              <div className="space-y-1">
                {filteredProfessionals.map((professional) => (
                  <Button
                    key={professional.id}
                    variant="ghost"
                    className="w-full justify-start h-auto p-3"
                    onClick={() => handleSelect(professional)}
                  >
                    <div className="flex items-center space-x-3 w-full">
                      <Stethoscope className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <div className="flex-1 text-left">
                        <div className="font-medium">{professional.name}</div>
                        <div className="text-sm text-muted-foreground flex items-center space-x-2">
                          {professional.specialty && (
                            <Badge variant="outline" className="text-xs">
                              {professional.specialty}
                            </Badge>
                          )}
                          {professional.phone && (
                            <span className="flex items-center">
                              <Phone className="h-3 w-3 mr-1" />
                              {professional.phone}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
