import { useState, useCallback, useRef } from 'react';
import { throttledFetch } from '@/lib/request-throttle';
import { useToast } from '@/hooks/use-toast';

interface UseApiRequestOptions {
  retryAttempts?: number;
  retryDelay?: number;
  showErrorToast?: boolean;
  showSuccessToast?: boolean;
  successMessage?: string;
  errorMessage?: string;
}

interface UseApiRequestReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  execute: (url: string, options?: RequestInit) => Promise<T | null>;
  reset: () => void;
}

export function useApiRequest<T = any>(
  options: UseApiRequestOptions = {}
): UseApiRequestReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);

  const {
    retryAttempts = 2,
    retryDelay = 1000,
    showErrorToast = true,
    showSuccessToast = false,
    successMessage,
    errorMessage
  } = options;

  const execute = useCallback(async (
    url: string, 
    requestOptions: RequestInit = {}
  ): Promise<T | null> => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    setLoading(true);
    setError(null);

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retryAttempts; attempt++) {
      try {
        console.log(`[API REQUEST] Attempt ${attempt + 1}/${retryAttempts + 1} for ${url}`);

        const response = await throttledFetch(url, {
          ...requestOptions,
          signal: abortController.signal
        });

        if (!response.ok) {
          // Don't retry on client errors (4xx) except 429 (rate limit)
          if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          
          // For server errors (5xx) and rate limits, we can retry
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        
        // Handle API response format
        const responseData = result.data !== undefined ? result.data : result;
        
        setData(responseData);
        
        if (showSuccessToast && successMessage) {
          toast({
            title: "Sucesso!",
            description: successMessage,
          });
        }

        console.log(`[API REQUEST] Success for ${url}`);
        return responseData;

      } catch (err) {
        lastError = err instanceof Error ? err : new Error('Erro desconhecido');
        
        // Don't retry if request was aborted
        if (lastError.name === 'AbortError') {
          console.log(`[API REQUEST] Request aborted for ${url}`);
          return null;
        }

        console.error(`[API REQUEST] Attempt ${attempt + 1} failed for ${url}:`, lastError);

        // If this is not the last attempt, wait before retrying
        if (attempt < retryAttempts) {
          const delay = retryDelay * Math.pow(2, attempt); // Exponential backoff
          console.log(`[API REQUEST] Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // All attempts failed
    setError(lastError);
    
    if (showErrorToast) {
      toast({
        title: "Erro",
        description: errorMessage || lastError?.message || 'Erro ao carregar dados',
        variant: "destructive"
      });
    }

    console.error(`[API REQUEST] All attempts failed for ${url}:`, lastError);
    return null;

  }, [retryAttempts, retryDelay, showErrorToast, showSuccessToast, successMessage, errorMessage, toast]);

  const reset = useCallback(() => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    reset
  };
}

// Specialized hooks for common use cases
export function useDashboardStats() {
  return useApiRequest({
    retryAttempts: 1, // Dashboard stats don't need aggressive retrying
    showErrorToast: false, // Handle errors gracefully without toasts
    errorMessage: 'Erro ao carregar estatísticas do dashboard'
  });
}

export function useUserManagement() {
  return useApiRequest({
    retryAttempts: 2,
    showErrorToast: true,
    showSuccessToast: true,
    errorMessage: 'Erro ao gerenciar usuários'
  });
}

export function useAppointmentData() {
  return useApiRequest({
    retryAttempts: 2,
    showErrorToast: true,
    errorMessage: 'Erro ao carregar dados de consultas'
  });
}
