/**
 * Request throttling utility to prevent API rate limiting
 */

interface ThrottleEntry {
  lastRequest: number;
  requestCount: number;
  resetTime: number;
}

class RequestThrottler {
  private requests: Map<string, ThrottleEntry> = new Map();
  private readonly maxRequestsPerMinute = 30; // Conservative limit
  private readonly minRequestInterval = 1000; // 1 second between requests
  private readonly windowSize = 60000; // 1 minute window

  /**
   * Check if a request should be throttled
   */
  shouldThrottle(endpoint: string): boolean {
    const now = Date.now();
    const entry = this.requests.get(endpoint);

    if (!entry) {
      // First request for this endpoint
      this.requests.set(endpoint, {
        lastRequest: now,
        requestCount: 1,
        resetTime: now + this.windowSize
      });
      return false;
    }

    // Reset counter if window has passed
    if (now > entry.resetTime) {
      entry.requestCount = 1;
      entry.resetTime = now + this.windowSize;
      entry.lastRequest = now;
      return false;
    }

    // Check if too soon since last request
    if (now - entry.lastRequest < this.minRequestInterval) {
      console.log(`[THROTTLE] Request to ${endpoint} throttled - too soon since last request`);
      return true;
    }

    // Check if too many requests in window
    if (entry.requestCount >= this.maxRequestsPerMinute) {
      console.log(`[THROTTLE] Request to ${endpoint} throttled - rate limit exceeded`);
      return true;
    }

    // Allow request and update counters
    entry.lastRequest = now;
    entry.requestCount++;
    return false;
  }

  /**
   * Get delay until next request is allowed
   */
  getDelayUntilNextRequest(endpoint: string): number {
    const entry = this.requests.get(endpoint);
    if (!entry) return 0;

    const now = Date.now();
    const timeSinceLastRequest = now - entry.lastRequest;
    
    if (timeSinceLastRequest < this.minRequestInterval) {
      return this.minRequestInterval - timeSinceLastRequest;
    }

    return 0;
  }

  /**
   * Wait for throttle delay if needed
   */
  async waitIfNeeded(endpoint: string): Promise<void> {
    const delay = this.getDelayUntilNextRequest(endpoint);
    if (delay > 0) {
      console.log(`[THROTTLE] Waiting ${delay}ms before request to ${endpoint}`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  /**
   * Clear throttle data for an endpoint
   */
  clearEndpoint(endpoint: string): void {
    this.requests.delete(endpoint);
  }

  /**
   * Clear all throttle data
   */
  clearAll(): void {
    this.requests.clear();
  }

  /**
   * Get current stats for debugging
   */
  getStats(): Record<string, ThrottleEntry> {
    const stats: Record<string, ThrottleEntry> = {};
    this.requests.forEach((entry, endpoint) => {
      stats[endpoint] = { ...entry };
    });
    return stats;
  }
}

// Global throttler instance
export const requestThrottler = new RequestThrottler();

/**
 * Throttled fetch wrapper
 */
export async function throttledFetch(
  url: string, 
  options: RequestInit = {}
): Promise<Response> {
  const endpoint = new URL(url, window.location.origin).pathname;
  
  // Check if request should be throttled
  if (requestThrottler.shouldThrottle(endpoint)) {
    await requestThrottler.waitIfNeeded(endpoint);
  }

  console.log(`[THROTTLE] Making request to ${endpoint}`);
  
  try {
    const response = await fetch(url, options);
    
    // Handle rate limiting response
    if (response.status === 429) {
      console.error(`[THROTTLE] Rate limited on ${endpoint}, backing off`);
      // Clear this endpoint to reset throttling
      requestThrottler.clearEndpoint(endpoint);
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    return response;
  } catch (error) {
    console.error(`[THROTTLE] Request failed for ${endpoint}:`, error);
    throw error;
  }
}

/**
 * Debounced function creator
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * Throttled function creator
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}
