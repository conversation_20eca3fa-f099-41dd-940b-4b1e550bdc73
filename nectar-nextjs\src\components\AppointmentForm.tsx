"use client"

import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { DateTimeInput } from '@/components/ui/datetime-input';
import { Calendar, Clock, User, Plus, Trash2, Search, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format, addMinutes } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatDateTimeForInput } from '@/lib/date-utils';
import { appointmentSchema, type AppointmentFormData } from '@/lib/validations';
import { useFormSubmission } from '@/hooks/useAsyncOperation';
import { PatientSearch, ProfessionalSearch } from '@/components/ui/smart-search';

// Utility functions for Brazilian timezone (UTC-3)
const brazilianTimezoneOffset = -3 * 60; // -3 hours in minutes

const toBrazilianTime = (date: Date | string | null): Date | null => {
  if (!date) return null;
  
  // Se for string, converte para Date
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Cria uma nova data ajustada para UTC-3
  const utcTime = dateObj.getTime() + (dateObj.getTimezoneOffset() * 60000);
  const brazilianTime = new Date(utcTime + (brazilianTimezoneOffset * 60000));
  
  return brazilianTime;
};

const formatDateTimeForBrazilianInput = (dateTime: Date | string | null | undefined): string => {
  if (!dateTime) return '';
  
  try {
    const date = toBrazilianTime(dateTime);
    if (!date || isNaN(date.getTime())) return '';
    
    // Formato para datetime-local: YYYY-MM-DDTHH:mm
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch (error) {
    console.error('[DATETIME] Error formatting date:', error);
    return '';
  }
};

const parseBrazilianDateTime = (dateTimeString: string): Date | null => {
  if (!dateTimeString) return null;
  
  try {
    // Se já tem timezone, usa diretamente
    if (dateTimeString.includes('T') && (dateTimeString.includes('Z') || dateTimeString.includes('-03:00'))) {
      return new Date(dateTimeString);
    }
    
    // Se é formato datetime-local (YYYY-MM-DDTHH:mm), adiciona timezone brasileiro
    if (dateTimeString.includes('T')) {
      return new Date(dateTimeString + ':00-03:00');
    }
    
    // Fallback para outros formatos
    const date = new Date(dateTimeString);
    return toBrazilianTime(date);
  } catch (error) {
    console.error('[DATETIME] Error parsing date:', error);
    return null;
  }
};

type Patient = {
  id: string;
  name: string;
  email?: string;
  phone?: string;
};

type HealthcareProfessional = {
  id: string;
  name: string;
  specialty: string | undefined;
  is_active: boolean;
};

interface AppointmentFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  patients: Patient[];
  healthcareProfessionals: HealthcareProfessional[];
  initialData?: Partial<AppointmentFormData & { id?: string }>;
  onSubmit: (data: AppointmentFormData & { id?: string }) => Promise<void>;
  loading?: boolean;
}

const AppointmentForm: React.FC<AppointmentFormProps> = ({
  open,
  onOpenChange,
  patients,
  healthcareProfessionals,
  initialData,
  onSubmit,
  loading = false
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('scheduling');

  // Check if we're in editing mode
  const isEditing = Boolean(initialData?.id);

  const { execute: submitForm, loading: submitting } = useFormSubmission({
    successMessage: isEditing ? 'Consulta atualizada com sucesso!' : 'Consulta agendada com sucesso!',
    errorMessage: isEditing ? 'Erro ao atualizar consulta' : 'Erro ao agendar consulta'
  });

  const form = useForm({
    resolver: zodResolver(appointmentSchema),
    defaultValues: {
      title: '',
      description: '',
      patient_id: '',
      healthcare_professional_id: '',
      start_time: '',
      end_time: '',
      status: 'scheduled',
      notes: '',
      has_recurrence: false,
      recurrence_type: 'weekly',
      recurrence_interval: 1,
      recurrence_days: [],
      recurrence_end_type: 'never',
      recurrence_end_date: '',
      recurrence_count: 1,
    }
  });

  const { control, handleSubmit, watch, setValue, reset, formState: { errors } } = form;

  const watchedValues = watch();

  // Initialize form with initial data
  useEffect(() => {
    if (initialData && open) {
      console.log('[DATETIME] Initializing form with data:', initialData);
      
      const formattedData = {
        ...initialData,
        start_time: formatDateTimeForBrazilianInput(initialData.start_time),
        end_time: formatDateTimeForBrazilianInput(initialData.end_time),
        patient_id: initialData.patient_id || '',
        healthcare_professional_id: initialData.healthcare_professional_id || '',
      };
      
      console.log('[DATETIME] Formatted data for form:', formattedData);
      reset(formattedData);
    }
  }, [initialData, open, reset]);

  // Auto-generate title when patient changes
  useEffect(() => {
    if (watchedValues.patient_id) {
      const patient = patients.find(p => p.id === watchedValues.patient_id);
      if (patient) {
        setValue('title', `Consulta - ${patient.name}`);
      }
    }
  }, [watchedValues.patient_id, patients, setValue]);

  // Auto-calculate end time based on start time (Brazilian timezone)
  // Only auto-calculate if end_time is not already set (e.g., from FullCalendar selection)
  useEffect(() => {
    console.log('[DATETIME] Auto-calculating end time, start_time:', watchedValues.start_time, 'end_time:', watchedValues.end_time);

    if (watchedValues.start_time && !watchedValues.end_time) {
      try {
        const startTime = parseBrazilianDateTime(watchedValues.start_time);

        console.log('[DATETIME] Parsed start time:', startTime, 'Valid:', startTime && !isNaN(startTime.getTime()));

        if (startTime && !isNaN(startTime.getTime())) {
          // Default 60 minutes duration for consultations
          const defaultDuration = 60;

          console.log('[DATETIME] Total duration:', defaultDuration, 'minutes');

          const endTime = addMinutes(startTime, defaultDuration);
          const formattedEndTime = formatDateTimeForBrazilianInput(endTime);

          console.log('[DATETIME] Calculated end time:', endTime, 'Formatted:', formattedEndTime);
          setValue('end_time', formattedEndTime);
        }
      } catch (error) {
        console.error('[DATETIME] Error calculating end time:', error);
      }
    }
  }, [watchedValues.start_time, watchedValues.end_time, setValue]);

  const onFormSubmit = async (data: AppointmentFormData) => {
    console.log('[DATETIME] Form submitted with data:', data);
    
    try {
      // Parse and validate dates in Brazilian timezone
      const startTime = parseBrazilianDateTime(data.start_time);
      const endTime = parseBrazilianDateTime(data.end_time);
      
      if (!startTime || !endTime) {
        toast({
          title: 'Erro',
          description: 'Datas inválidas. Por favor, verifique os horários.',
          variant: 'destructive'
        });
        return;
      }
      
      // Convert to ISO string with Brazilian timezone
      const formattedData = {
        ...data,
        type: 'consultation' as const, // Default type since field was removed
        start_time: startTime.toISOString(),
        end_time: endTime.toISOString(),
      };
      
      console.log('[DATETIME] Formatted data for submission:', formattedData);

      await submitForm(async () => {
        const appointmentData = {
          ...formattedData,
          status: formattedData.status || 'scheduled',
          ...(isEditing && initialData?.id && { id: initialData.id }),
        };

        console.log('[DATETIME] Final appointment data:', appointmentData);
        await onSubmit(appointmentData);

        // Reset form
        reset();
        setActiveTab('scheduling');
        onOpenChange(false);
      });
    } catch (error) {
      console.error('[DATETIME] Error submitting form:', error);
      toast({
        title: 'Erro',
        description: 'Erro ao processar os dados. Por favor, tente novamente.',
        variant: 'destructive'
      });
    }
  };

  const toggleRecurrenceDay = (day: number) => {
    const currentDays = watchedValues.recurrence_days || [];
    const newDays = currentDays.includes(day)
      ? currentDays.filter(d => d !== day)
      : [...currentDays, day];
    setValue('recurrence_days', newDays);
  };

  const weekDays = [
    { value: 1, label: 'D' }, // Domingo
    { value: 2, label: 'S' }, // Segunda
    { value: 3, label: 'T' }, // Terça
    { value: 4, label: 'Q' }, // Quarta
    { value: 5, label: 'Q' }, // Quinta
    { value: 6, label: 'S' }, // Sexta
    { value: 7, label: 'S' }, // Sábado
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Editar Consulta' : 'Agendar Nova Consulta'}</DialogTitle>
          <DialogDescription>
            {isEditing ? 'Edite os dados da consulta' : 'Preencha os dados para agendar uma nova consulta'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onFormSubmit)}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-1">
              <TabsTrigger value="scheduling" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Agendamento
              </TabsTrigger>
            </TabsList>

            <TabsContent value="scheduling" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="patient">Paciente *</Label>
                  <Controller
                    name="patient_id"
                    control={control}
                    render={({ field }) => (
                      <PatientSearch
                        patients={patients}
                        selectedPatientId={field.value}
                        onSelect={field.onChange}
                        placeholder="Buscar paciente por nome ou telefone..."
                      />
                    )}
                  />
                  {errors.patient_id && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.patient_id.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="professional">Profissional</Label>
                  <Controller
                    name="healthcare_professional_id"
                    control={control}
                    render={({ field }) => (
                      <ProfessionalSearch
                        professionals={healthcareProfessionals.filter(prof => prof.is_active)}
                        selectedProfessionalId={field.value}
                        onSelect={field.onChange}
                        placeholder="Buscar profissional por nome ou especialidade..."
                      />
                    )}
                  />
                  {errors.healthcare_professional_id && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.healthcare_professional_id.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="title">Título *</Label>
                <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="title"
                        {...field}
                      />
                    )}
                  />
                  {errors.title && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.title.message}
                    </p>
                  )}
                </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Controller
                    name="status"
                    control={control}
                    render={({ field }) => (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="scheduled">Agendado</SelectItem>
                          <SelectItem value="confirmed">Confirmado</SelectItem>
                          <SelectItem value="in_progress">Em Andamento</SelectItem>
                          <SelectItem value="cancelled">Cancelado</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.status && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.status.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  {/* Empty space for layout balance */}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start_time">Data/Hora Início *</Label>
                  <Controller
                    name="start_time"
                    control={control}
                    render={({ field }) => (
                      <DateTimeInput
                        id="start_time"
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="DD/MM/AAAA HH:mm"
                      />
                    )}
                  />
                  {errors.start_time && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.start_time.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end_time">Data/Hora Fim *</Label>
                  <Controller
                    name="end_time"
                    control={control}
                    render={({ field }) => (
                      <DateTimeInput
                        id="end_time"
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="DD/MM/AAAA HH:mm"
                      />
                    )}
                  />
                  {errors.end_time && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.end_time.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Descrição</Label>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      id="description"
                      placeholder="Descrição da consulta..."
                      {...field}
                    />
                  )}
                />
                {errors.description && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.description.message}
                  </p>
                )}
              </div>

              {/* Recurrence Section */}
              <Card>
                <CardHeader>
                  <div className="flex items-center space-x-2">
                    <Controller
                      name="has_recurrence"
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          id="has_recurrence"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      )}
                    />
                    <Label htmlFor="has_recurrence" className="text-sm font-medium">
                      Recorrência Personalizada
                    </Label>
                  </div>
                </CardHeader>

                {watchedValues.has_recurrence && (
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Repetir a cada:</Label>
                        <div className="flex items-center gap-2">
                          <Controller
                            name="recurrence_interval"
                            control={control}
                            render={({ field }) => (
                              <Input
                                type="number"
                                min="1"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                className="w-20"
                              />
                            )}
                          />
                          <Controller
                            name="recurrence_type"
                            control={control}
                            render={({ field }) => (
                              <Select value={field.value} onValueChange={field.onChange}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="daily">dia(s)</SelectItem>
                                  <SelectItem value="weekly">semana(s)</SelectItem>
                                  <SelectItem value="monthly">mês(es)</SelectItem>
                                </SelectContent>
                              </Select>
                            )}
                          />
                        </div>
                      </div>

                      {watchedValues.recurrence_type === 'weekly' && (
                        <div className="space-y-2">
                          <Label>Repetir:</Label>
                          <div className="flex gap-1">
                            {weekDays.map(day => (
                              <Button
                                key={day.value}
                                type="button"
                                variant={(watchedValues.recurrence_days || []).includes(day.value) ? 'default' : 'outline'}
                                size="sm"
                                className="w-8 h-8 p-0"
                                onClick={() => toggleRecurrenceDay(day.value)}
                              >
                                {day.label}
                              </Button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label>Termina em:</Label>
                      <Controller
                        name="recurrence_end_type"
                        control={control}
                        render={({ field }) => (
                          <RadioGroup value={field.value} onValueChange={field.onChange}>

                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="never" id="never" />
                          <Label htmlFor="never">Nunca</Label>
                        </div>
                        
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="date" id="end_date" />
                              <Label htmlFor="end_date">Em</Label>
                              <Controller
                                name="recurrence_end_date"
                                control={control}
                                render={({ field }) => (
                                  <Input
                                    type="date"
                                    {...field}
                                    disabled={watchedValues.recurrence_end_type !== 'date'}
                                    className="w-40"
                                  />
                                )}
                              />
                            </div>

                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="count" id="end_count" />
                              <Label htmlFor="end_count">Após</Label>
                              <Controller
                                name="recurrence_count"
                                control={control}
                                render={({ field }) => (
                                  <Input
                                    type="number"
                                    min="1"
                                    {...field}
                                    onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                    disabled={watchedValues.recurrence_end_type !== 'count'}
                                    className="w-20"
                                  />
                                )}
                              />
                              <Label>ocorrências</Label>
                            </div>
                          </RadioGroup>
                        )}
                      />
                    </div>
                  </CardContent>
                )}
              </Card>
            </TabsContent>

          </Tabs>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button type="submit" disabled={submitting || loading}>
              {submitting
                ? (isEditing ? 'Salvando...' : 'Agendando...')
                : (isEditing ? 'Salvar Alterações' : 'Agendar Consulta')
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AppointmentForm;
