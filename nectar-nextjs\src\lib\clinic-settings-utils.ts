import { SupabaseClient } from '@supabase/supabase-js'
import { createApiResponse, handleApiError } from '@/lib/api-utils'
import { debugLog } from '@/lib/debug-utils'

// Types
export type ClinicSettings = {
  id?: string;
  healthcare_professional_id: string;
  settings: any;
  created_at?: string;
  updated_at?: string;
}

export type ClinicSettingsInsert = {
  healthcare_professional_id: string;
  settings: any;
}

export type ClinicSettingsUpdate = {
  settings?: any;
  updated_at?: string;
}

// Default settings structure
export const DEFAULT_CLINIC_SETTINGS = {
  clinic_name: null,
  appointment_duration_minutes: 30,
  timezone: 'America/Sao_Paulo',
  weekly_schedule: {
    sunday: { enabled: false, working_hours_start: '08:00', working_hours_end: '18:00', break_intervals: [] },
    monday: { enabled: true, working_hours_start: '08:00', working_hours_end: '18:00', break_intervals: [] },
    tuesday: { enabled: true, working_hours_start: '08:00', working_hours_end: '18:00', break_intervals: [] },
    wednesday: { enabled: true, working_hours_start: '08:00', working_hours_end: '18:00', break_intervals: [] },
    thursday: { enabled: true, working_hours_start: '08:00', working_hours_end: '18:00', break_intervals: [] },
    friday: { enabled: true, working_hours_start: '08:00', working_hours_end: '18:00', break_intervals: [] },
    saturday: { enabled: false, working_hours_start: '08:00', working_hours_end: '18:00', break_intervals: [] }
  }
}

/**
 * Resolves healthcare professional ID from different input parameters
 */
export async function resolveHealthcareProfessionalId(
  supabase: SupabaseClient,
  userId: string,
  targetUserId?: string | null,
  healthcareProfessionalId?: string | null
): Promise<{ success: boolean; healthcareProfessionalId?: string; error?: any }> {
  
  debugLog.info('🔍 Resolving healthcare professional ID:', { 
    currentUserId: userId, 
    targetUserId, 
    healthcareProfessionalId 
  });

  // Get current user's role for permission checking
  const { data: currentUser, error: currentUserError } = await supabase
    .from('users')
    .select('role')
    .eq('id', userId)
    .single()

  if (currentUserError) {
    debugLog.error('❌ Error fetching current user role:', currentUserError);
    return { 
      success: false, 
      error: new Error('Failed to verify user permissions') 
    }
  }

  // If healthcare_professional_id is provided directly, validate it exists
  if (healthcareProfessionalId) {
    const { data: hpData, error: hpError } = await supabase
      .from('healthcare_professionals')
      .select('id, user_id')
      .eq('id', healthcareProfessionalId)
      .single()

    if (hpError) {
      debugLog.error('❌ Error finding healthcare professional by ID:', hpError);
      return { 
        success: false, 
        error: new Error('Healthcare professional not found') 
      }
    }

    // If user is a secretary, check if they have access to this healthcare professional
    if (currentUser.role === 'secretary' && hpData.user_id !== userId) {
      const { data: association, error: associationError } = await supabase
        .from('user_associations')
        .select('id')
        .eq('accessor_user_id', userId)
        .eq('target_user_id', hpData.user_id)
        .eq('association_type', 'secretary_to_doctor')
        .eq('is_active', true)
        .single()

      if (associationError || !association) {
        debugLog.error('❌ Secretary access denied - no valid association found:', {
          secretaryId: userId,
          targetUserId: hpData.user_id,
          error: associationError
        });
        return { 
          success: false, 
          error: new Error('Access denied: You do not have permission to access this healthcare professional settings') 
        }
      }

      debugLog.info('✅ Secretary access validated for healthcare professional');
    }

    debugLog.info('✅ Found healthcare professional:', { 
      healthcareProfessionalId, 
      userId: hpData.user_id 
    });
    
    return { 
      success: true, 
      healthcareProfessionalId: healthcareProfessionalId 
    }
  }

  // Use targetUserId if provided, otherwise use current userId
  const finalUserId = targetUserId || userId;

  // If user is a secretary trying to access another user's settings, validate access
  if (currentUser.role === 'secretary' && finalUserId !== userId) {
    const { data: association, error: associationError } = await supabase
      .from('user_associations')
      .select('id')
      .eq('accessor_user_id', userId)
      .eq('target_user_id', finalUserId)
      .eq('association_type', 'secretary_to_doctor')
      .eq('is_active', true)
      .single()

    if (associationError || !association) {
      debugLog.error('❌ Secretary access denied - no valid association found for target user');
      return { 
        success: false, 
        error: new Error('Access denied: You do not have permission to access this user settings') 
      }
    }

    debugLog.info('✅ Secretary access validated for target user');
  }
  
  const { data: healthcareProfessional, error: hpError } = await supabase
    .from('healthcare_professionals')
    .select('id')
    .eq('user_id', finalUserId)
    .single()

  if (hpError) {
    debugLog.error('❌ Error finding healthcare professional for user:', finalUserId, hpError);
    return { success: false, error: hpError }
  }

  debugLog.info('✅ Found healthcare professional for user:', { 
    userId: finalUserId, 
    healthcareProfessionalId: healthcareProfessional.id 
  });

  return { 
    success: true, 
    healthcareProfessionalId: healthcareProfessional.id 
  }
}

/**
 * Fetches clinic settings for a healthcare professional
 */
export async function fetchClinicSettings(
  supabase: SupabaseClient,
  healthcareProfessionalId: string
) {
  const { data: settings, error } = await supabase
    .from('clinic_settings')
    .select('*')
    .eq('healthcare_professional_id', healthcareProfessionalId)
    .single()

  if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
    debugLog.error('❌ Error fetching clinic settings:', error);
    throw error
  }

  // Return default settings if none exist
  if (!settings) {
    debugLog.info('ℹ️ No clinic settings found, returning defaults');
    return DEFAULT_CLINIC_SETTINGS
  }

  return settings.settings
}

/**
 * Creates or updates clinic settings
 */
export async function upsertClinicSettings(
  supabase: SupabaseClient,
  healthcareProfessionalId: string,
  settingsData: any,
  isUpdate: boolean = false
) {
  const processedSettings = {
    ...settingsData,
    appointment_duration_minutes: settingsData.appointment_duration_minutes 
      ? parseInt(settingsData.appointment_duration_minutes) 
      : (isUpdate ? undefined : 30),
    working_days: Array.isArray(settingsData.working_days) 
      ? settingsData.working_days 
      : (isUpdate ? undefined : [1, 2, 3, 4, 5])
  }

  if (isUpdate) {
    const updateData: ClinicSettingsUpdate = {
      settings: processedSettings,
      updated_at: new Date().toISOString()
    }

    const { data: settings, error } = await supabase
      .from('clinic_settings')
      .update(updateData)
      .eq('healthcare_professional_id', healthcareProfessionalId)
      .select()
      .single()

    if (error) {
      throw error
    }

    return settings.settings
  } else {
    const insertData: ClinicSettingsInsert = {
      healthcare_professional_id: healthcareProfessionalId,
      settings: processedSettings
    }

    const { data: settings, error } = await supabase
      .from('clinic_settings')
      .insert(insertData)
      .select()
      .single()

    if (error) {
      throw error
    }

    return settings.settings
  }
}
