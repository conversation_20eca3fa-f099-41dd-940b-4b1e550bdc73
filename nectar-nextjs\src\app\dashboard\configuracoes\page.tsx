"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { User, Building, Plus, Trash2, Clock, Calendar, ChevronDown, Users } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { makeAuthenticatedRequest } from '@/lib/api-client';
import { MaskedInput } from '@/components/ui/masked-input';
import { usePermissions } from '@/hooks/usePermissions';
import { useSelectedProfessional } from '@/contexts/SelectedProfessionalContext';
import { debugLog } from '@/lib/debug-utils';

type UserProfile = {
  name: string;
  email: string;
  phone: string;
  specialty: string;
  crm: string;
  address: string;
  cpf: string;
};

type WorkingDay = {
  enabled: boolean;
  working_hours_start: string;
  working_hours_end: string;
  break_intervals: { start: string; end: string; }[];
};

type ClinicSettings = {
  clinic_name: string | null;
  appointment_duration_minutes: number;
  timezone: string;
  weekly_schedule: {
    monday: WorkingDay;
    tuesday: WorkingDay;
    wednesday: WorkingDay;
    thursday: WorkingDay;
    friday: WorkingDay;
    saturday: WorkingDay;
    sunday: WorkingDay;
  };
};

const DayScheduleCard = ({ 
  dayKey, 
  dayConfig, 
  onUpdate 
}: { 
  dayKey: string; 
  dayConfig: WorkingDay; 
  onUpdate: (updatedDay: WorkingDay) => void; 
}) => {
  const dayNames: Record<string, string> = {
    sunday: 'Domingo',
    monday: 'Segunda-feira',
    tuesday: 'Terça-feira',
    wednesday: 'Quarta-feira',
    thursday: 'Quinta-feira',
    friday: 'Sexta-feira',
    saturday: 'Sábado'
  };

  const addBreakInterval = () => {
    const newInterval = { start: '12:00', end: '13:00' };
    onUpdate({
      ...dayConfig,
      break_intervals: [...(dayConfig.break_intervals || []), newInterval]
    });
  };

  const removeBreakInterval = (index: number) => {
    const newIntervals = dayConfig.break_intervals.filter((_, i) => i !== index);
    onUpdate({
      ...dayConfig,
      break_intervals: newIntervals
    });
  };

  const updateBreakInterval = (index: number, field: 'start' | 'end', value: string) => {
    const newIntervals = [...dayConfig.break_intervals];
    newIntervals[index] = { ...newIntervals[index], [field]: value };
    onUpdate({
      ...dayConfig,
      break_intervals: newIntervals
    });
  };

  // Função para gerar resumo do dia
  const getDaySummary = () => {
    if (!dayConfig.enabled) {
      return 'Fechado';
    }
    
    const start = dayConfig.working_hours_start || '08:00';
    const end = dayConfig.working_hours_end || '18:00';
    const breakCount = dayConfig.break_intervals?.length || 0;
    
    let summary = `${start} - ${end}`;
    if (breakCount > 0) {
      summary += ` (${breakCount} intervalo${breakCount > 1 ? 's' : ''})`;
    }
    
    return summary;
  };

  return (
    <AccordionItem value={dayKey} className="border rounded-lg">
      <AccordionTrigger className="px-4 py-3 hover:no-underline">
        <div className="flex items-center justify-between w-full pr-4">
          <div className="flex items-center space-x-3">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{dayNames[dayKey]}</span>
          </div>
          <div className="flex items-center space-x-3">
            <span className={`text-sm ${dayConfig.enabled ? 'text-green-600' : 'text-red-600'}`}>
              {getDaySummary()}
            </span>
            <Switch
              checked={dayConfig.enabled}
              onCheckedChange={(checked) => onUpdate({ ...dayConfig, enabled: checked })}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
      </AccordionTrigger>
      <AccordionContent className="px-4 pb-4">
        <div className="space-y-4">
          {dayConfig.enabled && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Horário de Início</Label>
                  <Input
                    type="time"
                    value={dayConfig.working_hours_start || '08:00'}
                    onChange={(e) => onUpdate({ ...dayConfig, working_hours_start: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Horário de Fim</Label>
                  <Input
                    type="time"
                    value={dayConfig.working_hours_end || '18:00'}
                    onChange={(e) => onUpdate({ ...dayConfig, working_hours_end: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Intervalos de Não Trabalho</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addBreakInterval}
                    className="h-8"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Adicionar Intervalo
                  </Button>
                </div>
                
                {dayConfig.break_intervals.map((interval, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 border rounded-md">
                    <Input
                      type="time"
                      value={interval.start}
                      onChange={(e) => updateBreakInterval(index, 'start', e.target.value)}
                      className="flex-1"
                    />
                    <span className="text-muted-foreground">até</span>
                    <Input
                      type="time"
                      value={interval.end}
                      onChange={(e) => updateBreakInterval(index, 'end', e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeBreakInterval(index)}
                      className="h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

const SettingsPage = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [healthcareProfessionals, setHealthcareProfessionals] = useState<any[]>([]);
  const { toast } = useToast();
  const { isAdmin, isSecretary, userRole, accessibleHealthcareProfessionals } = usePermissions();
  const { selectedProfessional, setSelectedProfessional } = useSelectedProfessional();

  // Estados iniciais com valores padrão
  const defaultProfile: UserProfile = {
    name: '',
    email: '',
    phone: '',
    specialty: '',
    crm: '',
    address: '',
    cpf: ''
  };

  const defaultWorkingDay: WorkingDay = {
    enabled: false,
    working_hours_start: '08:00',
    working_hours_end: '18:00',
    break_intervals: []
  };

  const defaultClinicSettings: ClinicSettings = {
    clinic_name: null,
    appointment_duration_minutes: 30,
    timezone: 'America/Sao_Paulo',
    weekly_schedule: {
      sunday: { ...defaultWorkingDay },
      monday: { ...defaultWorkingDay, enabled: true },
      tuesday: { ...defaultWorkingDay, enabled: true },
      wednesday: { ...defaultWorkingDay, enabled: true },
      thursday: { ...defaultWorkingDay, enabled: true },
      friday: { ...defaultWorkingDay, enabled: true },
      saturday: { ...defaultWorkingDay }
    }
  };

  const [profile, setProfile] = useState<UserProfile>(defaultProfile);
  const [clinicSettings, setClinicSettings] = useState<ClinicSettings>(defaultClinicSettings);

  // Fetch healthcare professionals when permissions are ready
  useEffect(() => {
    if (userRole) {
      fetchHealthcareProfessionals();
    }
  }, [userRole, isSecretary, accessibleHealthcareProfessionals]);

  // Auto-select first healthcare professional if none selected
  useEffect(() => {
    if (healthcareProfessionals.length > 0 && selectedProfessional === 'current') {
      const firstProfessional = healthcareProfessionals.find(prof => prof.is_active);
      if (firstProfessional) {
        setSelectedProfessional(firstProfessional.id);
      }
    }
  }, [healthcareProfessionals, selectedProfessional, setSelectedProfessional]);

  // Fetch settings when selected professional changes
  useEffect(() => {
    if (selectedProfessional && selectedProfessional !== 'current') {
      fetchSettings();
      fetchClinicSettings();
    }
  }, [selectedProfessional]);

  const fetchHealthcareProfessionals = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/healthcare-professionals');
      if (!response.ok) throw new Error('Failed to fetch healthcare professionals');
      const result = await response.json();
      const data = result.data || result;
      
      // Filter based on user permissions
      let professionals = Array.isArray(data) ? data : [];
      
      if (isSecretary && accessibleHealthcareProfessionals.length > 0) {
        professionals = professionals.filter(prof => 
          accessibleHealthcareProfessionals.includes(prof.id)
        );
      }
      
      setHealthcareProfessionals(professionals);
    } catch (error) {
      debugLog.error('Error fetching healthcare professionals:', error);
      setHealthcareProfessionals([]);
    }
  };

  const fetchSettings = async () => {
    try {
      setLoading(true);
      let url = '/api/settings';
      if (selectedProfessional && selectedProfessional !== 'current') {
        url += `?healthcare_professional_id=${selectedProfessional}`;
      }

      const response = await makeAuthenticatedRequest(url);
      if (!response.ok) throw new Error('Failed to fetch settings');
      const result = await response.json();
      const data = result.data || result;

      if (data.profile) {
        // Garantir que o perfil sempre tenha valores válidos
        const safeProfile = {
          ...defaultProfile,
          ...data.profile
        };
        setProfile(safeProfile);
      }
    } catch (error) {
      toast({
        title: "Erro ao carregar configurações",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
      // Em caso de erro, usar o perfil padrão
      setProfile(defaultProfile);
    } finally {
      setLoading(false);
    }
  };

  const fetchClinicSettings = async () => {
    try {
      let url = '/api/clinic-settings';
      if (selectedProfessional && selectedProfessional !== 'current') {
        url += `?healthcare_professional_id=${selectedProfessional}`;
      }

      const response = await makeAuthenticatedRequest(url);
      if (!response.ok) throw new Error('Failed to fetch clinic settings');
      const result = await response.json();
      const data = result.data || result;
      
      // Verificar se os dados estão no formato antigo e fazer migração
      if (data.working_days && !data.weekly_schedule) {
        const migratedSettings = migrateOldSettingsFormat(data);
        setClinicSettings(migratedSettings);
      } else if (data.weekly_schedule) {
        // Garantir que os dados sempre tenham valores válidos
        const safeSettings = {
          ...defaultClinicSettings,
          ...data,
          weekly_schedule: {
            ...defaultClinicSettings.weekly_schedule,
            ...data.weekly_schedule
          }
        };
        setClinicSettings(safeSettings);
      } else {
        setClinicSettings(defaultClinicSettings);
      }
    } catch (error) {
      debugLog.error('Error fetching clinic settings:', error);
      // Em caso de erro, usar as configurações padrão
      setClinicSettings(defaultClinicSettings);
    }
  };

  const migrateOldSettingsFormat = (oldData: any): ClinicSettings => {
    const daysMap = [
      'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'
    ];
    
    const weeklySchedule: any = {};
    
    daysMap.forEach((dayName, index) => {
      const dayNumber = index === 0 ? 7 : index; // Sunday is 7 in old format, 0 in new
      const isEnabled = Array.isArray(oldData.working_days) && oldData.working_days.includes(dayNumber);
      
      weeklySchedule[dayName] = {
        enabled: isEnabled,
        working_hours_start: oldData.working_hours_start || '08:00',
        working_hours_end: oldData.working_hours_end || '18:00',
        break_intervals: []
      };
    });

    return {
      clinic_name: oldData.clinic_name,
      appointment_duration_minutes: oldData.appointment_duration_minutes || 30,
      timezone: oldData.timezone || 'America/Sao_Paulo',
      weekly_schedule: weeklySchedule
    };
  };

  const saveProfile = async () => {
    try {
      setSaving(true);
      
      let url = '/api/settings/profile';
      if (selectedProfessional && selectedProfessional !== 'current') {
        url += `?healthcare_professional_id=${selectedProfessional}`;
      }

      const response = await makeAuthenticatedRequest(url, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profile)
      });

      if (!response.ok) throw new Error('Failed to save profile');

      toast({
        title: "Perfil atualizado",
        description: "As informações foram salvas com sucesso"
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar perfil",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const saveClinicSettings = async () => {
    try {
      setSaving(true);
      
      let url = '/api/clinic-settings';
      if (selectedProfessional && selectedProfessional !== 'current') {
        url += `?healthcare_professional_id=${selectedProfessional}`;
      }

      const response = await makeAuthenticatedRequest(url, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(clinicSettings)
      });

      if (!response.ok) throw new Error('Failed to save clinic settings');

      toast({
        title: "Configurações da clínica atualizadas",
        description: "As configurações foram salvas com sucesso"
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar configurações",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Configurações</h1>
          <p className="text-muted-foreground">
            {(isSecretary || isAdmin) && selectedProfessional && selectedProfessional !== 'current' 
              ? 'Gerencie o perfil e configurações do médico selecionado'
              : 'Gerencie seu perfil e configurações da clínica'
            }
          </p>
        </div>
        
        {/* Healthcare Professional Selector - only show for admin/secretary */}
        {(isSecretary || isAdmin) && healthcareProfessionals.length > 0 && (
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <Select value={selectedProfessional} onValueChange={setSelectedProfessional}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Selecionar médico" />
              </SelectTrigger>
              <SelectContent>
                {healthcareProfessionals
                  .filter(prof => prof.is_active)
                  .map(professional => (
                    <SelectItem key={professional.id} value={professional.id}>
                      {professional.name}
                      {professional.specialty && ` - ${professional.specialty}`}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="profile" className="flex items-center">
            <User className="mr-2 h-4 w-4" />
            Perfil
          </TabsTrigger>
          <TabsTrigger value="clinic" className="flex items-center">
            <Building className="mr-2 h-4 w-4" />
            Clínica
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5 text-primary" />
                Informações Pessoais
              </CardTitle>
              <CardDescription>
                Atualize suas informações pessoais e profissionais
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome Completo</Label>
                  <Input
                    id="name"
                    value={profile.name || ''}
                    onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profile.email || ''}
                    onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Telefone</Label>
                  <MaskedInput
                    id="phone"
                    mask="phone"
                    value={profile.phone || ''}
                    onChange={(value) => setProfile({ ...profile, phone: value })}
                    placeholder="(11) 99999-9999"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="specialty">Especialidade</Label>
                  <Input
                    id="specialty"
                    value={profile.specialty || ''}
                    onChange={(e) => setProfile({ ...profile, specialty: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="crm">CRM</Label>
                  <Input
                    id="crm"
                    value={profile.crm || ''}
                    onChange={(e) => setProfile({ ...profile, crm: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cpf">CPF</Label>
                  <MaskedInput
                    id="cpf"
                    mask="cpf"
                    value={profile.cpf || ''}
                    onChange={(value) => setProfile({ ...profile, cpf: value })}
                    placeholder="000.000.000-00"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Endereço</Label>
                <Textarea
                  id="address"
                  value={profile.address || ''}
                  onChange={(e) => setProfile({ ...profile, address: e.target.value })}
                  placeholder="Endereço completo"
                />
              </div>

              <Button onClick={saveProfile} disabled={saving}>
                {saving ? 'Salvando...' : 'Salvar Perfil'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="clinic">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="mr-2 h-5 w-5 text-primary" />
                Configurações da Clínica
              </CardTitle>
              <CardDescription>
                Configure horários de funcionamento e intervalos por dia da semana
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="clinic_name">Nome da Clínica</Label>
                <Input
                  id="clinic_name"
                  value={clinicSettings.clinic_name || ''}
                  onChange={(e) => setClinicSettings({ ...clinicSettings, clinic_name: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="appointment_duration">Duração Padrão da Consulta (minutos)</Label>
                  <Input
                    id="appointment_duration"
                    type="number"
                    min="15"
                    max="180"
                    step="15"
                    value={clinicSettings.appointment_duration_minutes || 30}
                    onChange={(e) => setClinicSettings({
                      ...clinicSettings,
                      appointment_duration_minutes: parseInt(e.target.value) || 30
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timezone">Fuso Horário</Label>
                  <Input
                    id="timezone"
                    value={clinicSettings.timezone || 'America/Sao_Paulo'}
                    onChange={(e) => setClinicSettings({ ...clinicSettings, timezone: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <Label className="text-base font-semibold">Horários de Funcionamento por Dia da Semana</Label>
                <Accordion type="multiple" className="space-y-2">
                  {Object.entries(clinicSettings.weekly_schedule || {}).map(([dayKey, dayConfig]) => (
                    <DayScheduleCard
                      key={dayKey}
                      dayKey={dayKey}
                      dayConfig={dayConfig}
                      onUpdate={(updatedDay) => {
                        setClinicSettings({
                          ...clinicSettings,
                          weekly_schedule: {
                            ...clinicSettings.weekly_schedule,
                            [dayKey]: updatedDay
                          }
                        });
                      }}
                    />
                  ))}
                </Accordion>
              </div>

              <Button onClick={saveClinicSettings} disabled={saving}>
                {saving ? 'Salvando...' : 'Salvar Configurações'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SettingsPage;
