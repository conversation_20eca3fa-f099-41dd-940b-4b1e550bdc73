import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { data: permissions, error } = await supabase
        .from('permissions')
        .select('*')
        .order('role_name')
        .order('resource')
        .order('action')

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(permissions || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user is admin using simplified role system
      const { data: currentUser, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (userError) {
        return handleApiError(userError)
      }

      const isAdmin = currentUser?.role === 'admin'
      if (!isAdmin) {
        return createApiResponse(null, 'Forbidden', 403)
      }

      const body = await request.json()
      const { role_name, resource, action } = body

      const { data: permission, error } = await supabase
        .from('permissions')
        .insert({
          role_name,
          resource,
          action
        })
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(permission, 'Permission created successfully', 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
