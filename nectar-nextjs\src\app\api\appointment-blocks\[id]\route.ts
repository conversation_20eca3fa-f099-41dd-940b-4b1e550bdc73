import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      const { error } = await supabase
        .from('appointment_blocks')
        .delete()
        .eq('id', id)

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse({ success: true })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
