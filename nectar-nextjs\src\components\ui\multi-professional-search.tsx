import React, { useState, useEffect, useRef } from 'react'
import { Search, Phone, Stethoscope } from 'lucide-react'
import { Input } from './input'
import { Card, CardContent } from './card'
import { Badge } from './badge'
import { cn } from '@/lib/utils'

interface HealthcareProfessional {
  id: string
  name: string
  phone?: string
  specialty?: string
}

interface MultiProfessionalSearchProps {
  professionals: HealthcareProfessional[]
  selectedProfessionalIds: string[]
  onSelectionChange: (ids: string[]) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function MultiProfessionalSearch({
  professionals,
  selectedProfessionalIds,
  onSelectionChange,
  placeholder = "Buscar profissionais por nome ou especialidade...",
  className,
  disabled = false
}: MultiProfessionalSearchProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [filteredProfessionals, setFilteredProfessionals] = useState<HealthcareProfessional[]>([])
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredProfessionals([])
      return
    }

    const filtered = professionals.filter((professional) => {
      const searchLower = searchTerm.toLowerCase()
      return (
        professional.name.toLowerCase().includes(searchLower) ||
        professional.specialty?.toLowerCase().includes(searchLower) ||
        professional.phone?.includes(searchTerm)
      )
    })

    setFilteredProfessionals(filtered.slice(0, 10))
  }, [searchTerm, professionals])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const toggleProfessional = (professionalId: string) => {
    if (selectedProfessionalIds.includes(professionalId)) {
      onSelectionChange(selectedProfessionalIds.filter(id => id !== professionalId))
    } else {
      onSelectionChange([...selectedProfessionalIds, professionalId])
    }
  }

  const selectedProfessionals = professionals.filter(p => selectedProfessionalIds.includes(p.id))

  return (
    <div className={cn("relative", className)} ref={containerRef}>
      <div className="space-y-2">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value)
              setIsOpen(true)
            }}
            onFocus={() => setIsOpen(true)}
            placeholder={placeholder}
            className="pl-10"
            disabled={disabled}
          />
        </div>

        {/* Selected Professionals */}
        {selectedProfessionals.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {selectedProfessionals.map((professional) => (
              <Badge
                key={professional.id}
                variant="secondary"
                className="flex items-center gap-1 pr-1"
              >
                <Stethoscope className="h-3 w-3" />
                <span className="text-sm">{professional.name}</span>
                {professional.specialty && (
                  <span className="text-xs text-muted-foreground">({professional.specialty})</span>
                )}
                <button
                  onClick={() => toggleProfessional(professional.id)}
                  className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5"
                  disabled={disabled}
                >
                  ×
                </button>
              </Badge>
            ))}
          </div>
        )}
      </div>

      {/* Search Results */}
      {isOpen && searchTerm.trim() !== '' && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-50 max-h-64 overflow-y-auto shadow-lg">
          <CardContent className="p-0">
            {filteredProfessionals.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                <Stethoscope className="mx-auto h-8 w-8 mb-2 opacity-50" />
                <p>Nenhum profissional encontrado</p>
              </div>
            ) : (
              <div className="max-h-48 overflow-y-auto">
                {filteredProfessionals.map((professional) => {
                  const isSelected = selectedProfessionalIds.includes(professional.id)
                  return (
                    <div
                      key={professional.id}
                      className={cn(
                        "p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0 transition-colors",
                        isSelected && "bg-primary/10"
                      )}
                      onClick={() => toggleProfessional(professional.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-start space-x-3">
                          <div className="bg-primary/10 p-2 rounded-full">
                            <Stethoscope className="h-4 w-4 text-primary" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-gray-900 truncate">{professional.name}</p>
                            {professional.specialty && (
                              <p className="text-sm text-gray-600">{professional.specialty}</p>
                            )}
                            {professional.phone && (
                              <div className="flex items-center text-sm text-gray-500 mt-1">
                                <Phone className="h-3 w-3 mr-1" />
                                {professional.phone}
                              </div>
                            )}
                          </div>
                        </div>
                        {isSelected && (
                          <div className="text-primary font-medium">✓</div>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
