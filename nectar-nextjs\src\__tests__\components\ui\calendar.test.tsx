import { render, screen, fireEvent } from '@testing-library/react'
import { Calendar } from '@/components/ui/calendar'

// Mock react-day-picker
jest.mock('react-day-picker', () => ({
  DayPicker: ({ onDayClick, modifiers, ...props }: any) => (
    <div data-testid="day-picker" {...props}>
      <button 
        data-testid="day-1" 
        onClick={() => onDayClick?.(new Date('2024-01-01'))}
        className={modifiers?.hasAppointments ? 'has-appointments' : ''}
      >
        1
      </button>
      <button 
        data-testid="day-2" 
        onClick={() => onDayClick?.(new Date('2024-01-02'))}
      >
        2
      </button>
    </div>
  )
}))

describe('Calendar', () => {
  it('should render calendar component', () => {
    render(<Calendar />)
    
    expect(screen.getByTestId('day-picker')).toBeInTheDocument()
  })

  it('should handle day click events', () => {
    const mockOnDayClick = jest.fn()
    
    render(<Calendar onDayClick={mockOnDayClick} />)
    
    fireEvent.click(screen.getByTestId('day-1'))
    
    expect(mockOnDayClick).toHaveBeenCalledWith(new Date('2024-01-01'))
  })

  it('should show appointment indicators', () => {
    const appointmentCounts = {
      '2024-01-01': 3
    }
    
    render(<Calendar appointmentCounts={appointmentCounts} />)
    
    const dayWithAppointments = screen.getByTestId('day-1')
    expect(dayWithAppointments).toHaveClass('has-appointments')
  })

  it('should handle selected date', () => {
    const selectedDate = new Date('2024-01-01')
    
    render(<Calendar selected={selectedDate} />)
    
    expect(screen.getByTestId('day-picker')).toBeInTheDocument()
  })

  it('should apply custom class names', () => {
    const customClassNames = {
      day: 'custom-day-class'
    }
    
    render(<Calendar classNames={customClassNames} />)
    
    expect(screen.getByTestId('day-picker')).toBeInTheDocument()
  })

  it('should handle mode prop', () => {
    render(<Calendar mode="single" />)
    
    expect(screen.getByTestId('day-picker')).toBeInTheDocument()
  })

  it('should handle disabled dates', () => {
    const disabledDates = [new Date('2024-01-01')]
    
    render(<Calendar disabled={disabledDates} />)
    
    expect(screen.getByTestId('day-picker')).toBeInTheDocument()
  })

  it('should show outside days when showOutsideDays is true', () => {
    render(<Calendar showOutsideDays={true} />)
    
    expect(screen.getByTestId('day-picker')).toBeInTheDocument()
  })

  it('should hide outside days when showOutsideDays is false', () => {
    render(<Calendar showOutsideDays={false} />)
    
    expect(screen.getByTestId('day-picker')).toBeInTheDocument()
  })

  it('should handle appointment counts with zero appointments', () => {
    const appointmentCounts = {
      '2024-01-01': 0,
      '2024-01-02': 2
    }
    
    render(<Calendar appointmentCounts={appointmentCounts} />)
    
    expect(screen.getByTestId('day-picker')).toBeInTheDocument()
  })

  it('should handle empty appointment counts', () => {
    render(<Calendar appointmentCounts={{}} />)
    
    expect(screen.getByTestId('day-picker')).toBeInTheDocument()
  })
})
