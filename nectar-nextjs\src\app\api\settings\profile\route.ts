import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import { debugLog } from '@/lib/debug-utils'

export async function PATCH(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      // Check if a specific user_id or healthcare_professional_id is provided (for admin/secretary access)
      const url = new URL(request.url)
      const targetUserId = url.searchParams.get('user_id')
      const healthcareProfessionalId = url.searchParams.get('healthcare_professional_id')

      let finalUserId = userId;

      // Get current user's role for permission checking
      const { data: currentUser, error: currentUserError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (currentUserError) {
        debugLog.error('❌ Error fetching current user role:', currentUserError);
        return handleApiError(new Error('Failed to verify user permissions'))
      }

      // If healthcare_professional_id is provided, get the corresponding user_id
      if (healthcareProfessionalId) {
        const { data: hpData, error: hpError } = await supabase
          .from('healthcare_professionals')
          .select('user_id')
          .eq('id', healthcareProfessionalId)
          .single()

        if (hpError) {
          debugLog.error('❌ Error finding healthcare professional by ID:', hpError);
          return handleApiError(new Error('Healthcare professional not found'))
        }

        finalUserId = hpData.user_id;

        // If user is a secretary, check if they have access to this healthcare professional
        if (currentUser.role === 'secretary' && finalUserId !== userId) {
          const { data: association, error: associationError } = await supabase
            .from('user_associations')
            .select('id')
            .eq('accessor_user_id', userId)
            .eq('target_user_id', finalUserId)
            .eq('association_type', 'secretary_to_doctor')
            .eq('is_active', true)
            .single()

          if (associationError || !association) {
            debugLog.error('❌ Secretary access denied for profile update');
            return createApiResponse(undefined, 'Access denied: You do not have permission to update this healthcare professional profile', 403)
          }
        }
      } else if (targetUserId) {
        finalUserId = targetUserId;
        
        // If user is a secretary trying to update another user's profile, validate access
        if (currentUser.role === 'secretary' && finalUserId !== userId) {
          const { data: association, error: associationError } = await supabase
            .from('user_associations')
            .select('id')
            .eq('accessor_user_id', userId)
            .eq('target_user_id', finalUserId)
            .eq('association_type', 'secretary_to_doctor')
            .eq('is_active', true)
            .single()

          if (associationError || !association) {
            debugLog.error('❌ Secretary access denied for profile update');
            return createApiResponse(undefined, 'Access denied: You do not have permission to update this user profile', 403)
          }
        }
      }
      
      // Get user role to determine where to save data
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', finalUserId)
        .single()

      if (userError) {
        throw new Error('Failed to fetch user data')
      }

      // Update users table
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          name: body.name,
          email: body.email,
          phone: body.phone,
          updated_at: new Date().toISOString()
        })
        .eq('id', finalUserId)

      if (userUpdateError) {
        throw new Error('Failed to update user profile')
      }

      // If user is a healthcare professional, update healthcare_professionals table
      if (userData.role === 'healthcare_professional') {
        // Check if healthcare professional record exists
        const { data: hpData, error: hpCheckError } = await supabase
          .from('healthcare_professionals')
          .select('id')
          .eq('user_id', finalUserId)
          .single()

        if (hpCheckError && hpCheckError.code !== 'PGRST116') {
          throw new Error('Failed to check healthcare professional record')
        }

        const hpUpdateData = {
          name: body.name,
          email: body.email,
          phone: body.phone,
          specialty: body.specialty || null,
          crm: body.crm || null,
          address: body.address || null,
          cpf: body.cpf || null,
          updated_at: new Date().toISOString()
        }

        if (hpData) {
          // Update existing record
          const { error: hpUpdateError } = await supabase
            .from('healthcare_professionals')
            .update(hpUpdateData)
            .eq('user_id', finalUserId)

          if (hpUpdateError) {
            throw new Error('Failed to update healthcare professional profile')
          }
        } else {
          // Create new record
          const { error: hpCreateError } = await supabase
            .from('healthcare_professionals')
            .insert({
              user_id: finalUserId,
              ...hpUpdateData
            })

          if (hpCreateError) {
            throw new Error('Failed to create healthcare professional profile')
          }
        }
      }
      
      return createApiResponse({ 
        message: 'Profile updated successfully',
        profile: body 
      })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
