import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function DELETE(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json();
      const { appointment_id } = body;

      if (!appointment_id) {
        return handleApiError(new Error('appointment_id is required'));
      }

      // Verify the appointment belongs to the user
      const { data: appointment, error: appointmentError } = await supabase
        .from('appointments')
        .select('id, user_id')
        .eq('id', appointment_id)
        .eq('user_id', userId)
        .single();

      if (appointmentError || !appointment) {
        return handleApiError(new Error('Appointment not found or access denied'));
      }

      // Delete all draft medical records for this appointment
      const { error: deleteError } = await supabase
        .from('medical_records')
        .delete()
        .eq('appointment_id', appointment_id)
        .eq('user_id', userId)
        .eq('is_draft', true);

      if (deleteError) {
        console.error('Error deleting draft records:', deleteError);
        return handleApiError(deleteError);
      }

      return createApiResponse({
        success: true,
        message: 'Draft records cleaned up successfully'
      });

    } catch (error) {
      return handleApiError(error);
    }
  });
}
