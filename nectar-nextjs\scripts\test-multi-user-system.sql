-- Test Script for Multi-User System
-- This script tests the multi-user functionality and security

-- 1. Create test users
DO $$
DECLARE
  admin_id UUID;
  doctor1_id UUID;
  doctor2_id UUID;
  secretary1_id UUID;
BEGIN
  -- Create admin user
  INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, raw_user_meta_data)
  VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    NOW(),
    NOW(),
    NOW(),
    '{"name": "Admin Teste", "role": "admin"}'::jsonb
  )
  ON CONFLICT (email) DO NOTHING
  RETURNING id INTO admin_id;

  -- Create doctor 1
  INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, raw_user_meta_data)
  VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    NOW(),
    NOW(),
    NOW(),
    '{"name": "<PERSON><PERSON>", "role": "doctor"}'::jsonb
  )
  ON CONFLICT (email) DO NOTHING
  RETURNING id INTO doctor1_id;

  -- Create doctor 2
  INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, raw_user_meta_data)
  VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    NOW(),
    NOW(),
    NOW(),
    '{"name": "Dr. Maria Teste", "role": "doctor"}'::jsonb
  )
  ON CONFLICT (email) DO NOTHING
  RETURNING id INTO doctor2_id;

  -- Create secretary
  INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, raw_user_meta_data)
  VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    NOW(),
    NOW(),
    NOW(),
    '{"name": "Maria Secretária Teste", "role": "secretary"}'::jsonb
  )
  ON CONFLICT (email) DO NOTHING
  RETURNING id INTO secretary1_id;

  RAISE NOTICE 'Test users created successfully';
END $$;

-- 2. Test trigger functionality
SELECT 'Testing trigger functionality...' as test_step;

-- Check if public.users were created
SELECT 
  'public.users sync' as test,
  CASE 
    WHEN COUNT(*) >= 4 THEN 'PASS'
    ELSE 'FAIL'
  END as result,
  COUNT(*) as count
FROM users 
WHERE email LIKE '%.<EMAIL>';

-- Check if user_roles were created
SELECT 
  'user_roles creation' as test,
  CASE 
    WHEN COUNT(*) >= 4 THEN 'PASS'
    ELSE 'FAIL'
  END as result,
  COUNT(*) as count
FROM user_roles ur
JOIN users u ON ur.user_id = u.id
WHERE u.email LIKE '%.<EMAIL>';

-- Check if healthcare_professionals were created for doctors
SELECT 
  'healthcare_professionals creation' as test,
  CASE 
    WHEN COUNT(*) >= 2 THEN 'PASS'
    ELSE 'FAIL'
  END as result,
  COUNT(*) as count
FROM healthcare_professionals hp
JOIN users u ON hp.user_id = u.id
WHERE u.email LIKE '<EMAIL>';

-- Check if secretaries were created
SELECT 
  'secretaries creation' as test,
  CASE 
    WHEN COUNT(*) >= 1 THEN 'PASS'
    ELSE 'FAIL'
  END as result,
  COUNT(*) as count
FROM secretaries s
JOIN users u ON s.user_id = u.id
WHERE u.email LIKE '<EMAIL>';

-- 3. Test association creation
DO $$
DECLARE
  doctor1_id UUID;
  secretary1_id UUID;
  admin_id UUID;
BEGIN
  -- Get user IDs
  SELECT id INTO doctor1_id FROM users WHERE email = '<EMAIL>';
  SELECT id INTO secretary1_id FROM users WHERE email = '<EMAIL>';
  SELECT id INTO admin_id FROM users WHERE email = '<EMAIL>';

  -- Create association: secretary to doctor
  INSERT INTO user_associations (
    accessor_user_id,
    target_user_id,
    association_type,
    created_by,
    is_active
  )
  VALUES (
    secretary1_id,
    doctor1_id,
    'secretary_to_doctor',
    admin_id,
    true
  )
  ON CONFLICT (accessor_user_id, target_user_id, association_type) DO NOTHING;

  RAISE NOTICE 'Test association created';
END $$;

-- 4. Test access functions
SELECT 'Testing access functions...' as test_step;

-- Test has_access_to_user function
DO $$
DECLARE
  doctor1_id UUID;
  secretary1_id UUID;
  doctor2_id UUID;
  has_access_result BOOLEAN;
BEGIN
  SELECT id INTO doctor1_id FROM users WHERE email = '<EMAIL>';
  SELECT id INTO secretary1_id FROM users WHERE email = '<EMAIL>';
  SELECT id INTO doctor2_id FROM users WHERE email = '<EMAIL>';

  -- Test 1: Secretary should have access to associated doctor
  -- Note: This test simulates the secretary being the current user
  -- In real usage, auth.uid() would return the secretary's ID
  
  -- Test 2: Secretary should NOT have access to non-associated doctor
  -- This would also need to be tested with proper auth context
  
  RAISE NOTICE 'Access function tests completed (manual verification needed with proper auth context)';
END $$;

-- 5. Test get_accessible_users function
SELECT 'Testing get_accessible_users function...' as test_step;

-- This function requires auth.uid() context, so we'll test the structure
SELECT 
  'get_accessible_users structure' as test,
  CASE 
    WHEN COUNT(*) > 0 THEN 'PASS'
    ELSE 'FAIL'
  END as result
FROM information_schema.routines 
WHERE routine_name = 'get_accessible_users';

-- 6. Test RLS policies
SELECT 'Testing RLS policies...' as test_step;

-- Check if RLS is enabled on key tables
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('appointments', 'patients', 'medical_records', 'user_associations')
ORDER BY tablename;

-- 7. Test permissions system
SELECT 'Testing permissions system...' as test_step;

-- Check if secretary permissions exist
SELECT 
  'secretary permissions' as test,
  CASE 
    WHEN COUNT(*) > 0 THEN 'PASS'
    ELSE 'FAIL'
  END as result,
  COUNT(*) as permission_count
FROM permissions 
WHERE role_name = 'secretary';

-- 8. Performance test - check indexes
SELECT 'Testing performance indexes...' as test_step;

SELECT 
  schemaname,
  tablename,
  indexname,
  indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
  AND tablename IN ('user_associations', 'user_access_logs')
ORDER BY tablename, indexname;

-- 9. Security test - check constraints
SELECT 'Testing security constraints...' as test_step;

SELECT 
  conname as constraint_name,
  contype as constraint_type,
  pg_get_constraintdef(oid) as definition
FROM pg_constraint 
WHERE conrelid = 'user_associations'::regclass
ORDER BY conname;

-- 10. Test data integrity
SELECT 'Testing data integrity...' as test_step;

-- Check for orphaned associations
SELECT 
  'orphaned associations' as test,
  CASE 
    WHEN COUNT(*) = 0 THEN 'PASS'
    ELSE 'FAIL'
  END as result,
  COUNT(*) as orphaned_count
FROM user_associations ua
WHERE NOT EXISTS (
  SELECT 1 FROM users u1 WHERE u1.id = ua.accessor_user_id
) OR NOT EXISTS (
  SELECT 1 FROM users u2 WHERE u2.id = ua.target_user_id
);

-- 11. Summary report
SELECT 'MULTI-USER SYSTEM TEST SUMMARY' as summary;

SELECT 
  'Total test users created' as metric,
  COUNT(*) as value
FROM users 
WHERE email LIKE '%.<EMAIL>';

SELECT 
  'Total associations created' as metric,
  COUNT(*) as value
FROM user_associations ua
JOIN users u1 ON ua.accessor_user_id = u1.id
JOIN users u2 ON ua.target_user_id = u2.id
WHERE u1.email LIKE '%.<EMAIL>' 
   OR u2.email LIKE '%.<EMAIL>';

SELECT 
  'RLS enabled tables' as metric,
  COUNT(*) as value
FROM pg_tables 
WHERE schemaname = 'public' 
  AND rowsecurity = true;

-- Cleanup instructions
SELECT '
CLEANUP INSTRUCTIONS:
To remove test data, run:

DELETE FROM user_associations 
WHERE accessor_user_id IN (SELECT id FROM users WHERE email LIKE ''%.<EMAIL>'')
   OR target_user_id IN (SELECT id FROM users WHERE email LIKE ''%.<EMAIL>'');

DELETE FROM user_roles 
WHERE user_id IN (SELECT id FROM users WHERE email LIKE ''%.<EMAIL>'');

DELETE FROM healthcare_professionals 
WHERE user_id IN (SELECT id FROM users WHERE email LIKE ''%.<EMAIL>'');

DELETE FROM secretaries 
WHERE user_id IN (SELECT id FROM users WHERE email LIKE ''%.<EMAIL>'');

DELETE FROM users WHERE email LIKE ''%.<EMAIL>'';

DELETE FROM auth.users WHERE email LIKE ''%.<EMAIL>'';
' as cleanup_instructions;
