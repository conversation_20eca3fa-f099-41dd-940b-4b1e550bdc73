import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type HealthcareProfessional = Tables<'healthcare_professionals'>
type HealthcareProfessionalInsert = TablesInsert<'healthcare_professionals'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      console.log('🔍 Healthcare Professionals API - User ID:', userId)

      // Get accessible users first
      const { data: accessibleUsers, error: accessError } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      console.log('📋 Accessible users result:', { accessibleUsers, accessError })

      if (accessError) {
        console.error('❌ Error getting accessible users:', accessError)
        return handleApiError(accessError)
      }

      // Filter only healthcare professionals from accessible users
      const healthcareProfessionalUserIds = accessibleUsers
        ?.filter((user: any) => user.role === 'healthcare_professional')
        ?.map((user: any) => user.user_id) || []

      console.log('👨‍⚕️ Healthcare professional user IDs found:', healthcareProfessionalUserIds)

      if (healthcareProfessionalUserIds.length === 0) {
        console.log('⚠️ No healthcare professional user IDs found, returning empty array')
        return createApiResponse([])
      }

      // Use RPC to bypass RLS and get professionals directly
      const { data: professionals, error } = await supabase
        .rpc('get_healthcare_professionals_for_user', {
          requesting_user_id: userId,
          target_user_ids: healthcareProfessionalUserIds
        })

      console.log('🏥 Healthcare professionals query result:', { professionals, error })

      if (error) {
        console.error('❌ Error fetching healthcare professionals:', error)
        return handleApiError(error)
      }

      console.log('✅ Returning professionals:', professionals?.length || 0)
      return createApiResponse(professionals || [])
    } catch (error) {
      console.error('💥 Unexpected error in healthcare professionals API:', error)
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const professionalData: HealthcareProfessionalInsert = {
        ...body,
        user_id: userId,
        is_active: body.is_active ?? true
      }

      const { data: professional, error } = await supabase
        .from('healthcare_professionals')
        .insert(professionalData)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(professional, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
