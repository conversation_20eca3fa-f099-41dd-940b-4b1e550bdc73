import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const appointmentId = searchParams.get('appointment_id')

      let query = supabase
        .from('consultation_attachments')
        .select('*')
        .eq('user_id', userId)

      if (appointmentId) {
        query = query.eq('appointment_id', appointmentId)
      }

      const { data: attachments, error } = await query.order('created_at', { ascending: false })

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(attachments || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const formData = await request.formData()
      const file = formData.get('file') as File
      const appointmentId = formData.get('appointment_id') as string
      const uploadedByName = formData.get('uploaded_by_name') as string

      if (!file || !appointmentId || !uploadedByName) {
        return createApiResponse(null, 'Missing required fields', 400)
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (file.size > maxSize) {
        return createApiResponse(null, 'Arquivo muito grande. Tamanho máximo: 10MB', 400)
      }

      // Validate file type
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain'
      ]
      
      if (!allowedTypes.includes(file.type)) {
        return createApiResponse(null, 'Tipo de arquivo não permitido', 400)
      }

      // Verify appointment belongs to user
      const { data: appointment, error: appointmentError } = await supabase
        .from('appointments')
        .select('id')
        .eq('id', appointmentId)
        .eq('user_id', userId)
        .single()

      if (appointmentError || !appointment) {
        return createApiResponse(null, 'Consulta não encontrada', 404)
      }

      // Generate unique file name
      const timestamp = Date.now()
      const fileExtension = file.name.split('.').pop()
      const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
      const uniqueFileName = `${timestamp}_${sanitizedFileName}`
      const filePath = `${userId}/${appointmentId}/${uniqueFileName}`

      // Upload file to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('consultation-attachments')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (uploadError) {
        console.error('Upload error:', uploadError)
        return handleApiError(uploadError)
      }

      // Save attachment record to database
      const { data: attachment, error: dbError } = await supabase
        .from('consultation_attachments')
        .insert({
          appointment_id: appointmentId,
          user_id: userId,
          file_name: file.name,
          file_path: uploadData.path,
          file_size: file.size,
          file_type: file.type,
          uploaded_by: userId,
          uploaded_by_name: uploadedByName
        })
        .select()
        .single()

      if (dbError) {
        // Clean up uploaded file if database insert fails
        await supabase.storage
          .from('consultation-attachments')
          .remove([uploadData.path])
        
        return handleApiError(dbError)
      }

      return createApiResponse(attachment, 'Arquivo enviado com sucesso', 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function DELETE(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const attachmentId = searchParams.get('id')

      if (!attachmentId) {
        return createApiResponse(null, 'ID do anexo é obrigatório', 400)
      }

      // Get attachment details
      const { data: attachment, error: fetchError } = await supabase
        .from('consultation_attachments')
        .select('*')
        .eq('id', attachmentId)
        .eq('user_id', userId)
        .single()

      if (fetchError || !attachment) {
        return createApiResponse(null, 'Anexo não encontrado', 404)
      }

      // Delete file from storage
      const { error: storageError } = await supabase.storage
        .from('consultation-attachments')
        .remove([attachment.file_path])

      if (storageError) {
        console.error('Storage deletion error:', storageError)
        // Continue with database deletion even if storage deletion fails
      }

      // Delete attachment record from database
      const { error: deleteError } = await supabase
        .from('consultation_attachments')
        .delete()
        .eq('id', attachmentId)
        .eq('user_id', userId)

      if (deleteError) {
        return handleApiError(deleteError)
      }

      return createApiResponse(null, 'Anexo removido com sucesso')
    } catch (error) {
      return handleApiError(error)
    }
  })
}
