@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here.
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Healthcare design system - Professional and trustworthy */
    --background: 220 14% 98%;
    --foreground: 215 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 20% 15%;

    /* Medical blue - professional and trustworthy */
    --primary: 207 73% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 207 73% 65%;

    /* Clean medical green */
    --secondary: 160 35% 90%;
    --secondary-foreground: 160 70% 25%;

    --muted: 215 20% 95%;
    --muted-foreground: 215 15% 45%;

    /* Warm accent for friendliness */
    --accent: 25 85% 95%;
    --accent-foreground: 25 85% 35%;

    --destructive: 0 70% 55%;
    --destructive-foreground: 0 0% 100%;

    --border: 215 20% 90%;
    --input: 215 20% 95%;
    --ring: 207 73% 45%;

    /* Healthcare specific colors */
    --success: 160 70% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 38 85% 55%;
    --warning-foreground: 0 0% 100%;
    --info: 207 73% 55%;
    --info-foreground: 0 0% 100%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-glow)) 100%);
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--muted)));

    /* Shadows */
    --shadow-soft: 0 2px 8px -2px hsl(var(--primary) / 0.1);
    --shadow-medium: 0 4px 16px -4px hsl(var(--primary) / 0.15);
    --shadow-strong: 0 8px 32px -8px hsl(var(--primary) / 0.2);

    /* Animation */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Calendar availability indicators */
@layer components {
  /* Days with available time slots - green background */
  .has-availability button {
    background-color: #97d698 !important; /* green-50 */
    border-color: #97d698 !important; /* green-200 */
    color: rgb(0, 0, 0) !important;
  }

  .has-availability button:hover {
    background-color: #16a34a !important; /* green-100 */
  }

  /* Selected day with availability */
  .has-availability [data-selected="true"] {
    background-color: #16a34a !important; /* green-600 */
    color: rgb(0, 0, 0) !important;
  }

  /* Make sure green background is visible */
  .has-availability td {
    background-color: rgba(240, 253, 244, 0.8) !important;
  }

  .has-availability td:hover {
    background-color: rgba(220, 252, 231, 1) !important;
  }

  /* Desktop calendar optimizations - larger size for web */
  @media (min-width: 641px) {
    [data-slot="calendar"] {
      padding: 1.5rem;
      min-width: 380px;
      width: 100%;
    }
    
    [data-slot="calendar"] .rdp-month_caption {
      margin-bottom: 1rem;
      font-size: 1rem;
    }
    
    [data-slot="calendar"] .rdp-weekday {
      font-size: 0.875rem;
      padding: 0.75rem 0.25rem;
      min-width: 3rem;
    }
    
    [data-slot="calendar"] button {
      min-height: 2.75rem !important;
      min-width: 2.75rem !important;
      font-size: 0.875rem !important;
    }
    
    [data-slot="calendar"] .rdp-table {
      border-spacing: 6px;
      width: 100%;
    }
    
    [data-slot="calendar"] .rdp-day {
      padding: 0.25rem;
      min-width: 3rem;
    }
    
    [data-slot="calendar"] .rdp-weekdays,
    [data-slot="calendar"] .rdp-week {
      width: 100%;
    }
  }
  
  /* Even larger for lg screens */
  @media (min-width: 1024px) {
    [data-slot="calendar"] {
      min-width: 420px;
    }
    
    [data-slot="calendar"] button {
      min-height: 3rem !important;
      min-width: 3rem !important;
    }
    
    [data-slot="calendar"] .rdp-weekday,
    [data-slot="calendar"] .rdp-day {
      min-width: 3.5rem;
    }
  }

  /* Mobile calendar optimizations - only apply to small screens */
  @media (max-width: 640px) {
    /* Mobile-specific container adjustments */
    .space-y-6 {
      gap: 0.75rem;
    }
    
    /* Calendar container mobile adjustments */
    [data-slot="calendar"] {
      font-size: 0.875rem;
      padding: 0.75rem 0.5rem;
      width: 100%;
      max-width: 100%;
      min-width: 280px;
    }
    
    [data-slot="calendar"] .rdp-month_caption {
      font-size: 0.875rem;
      height: 2rem;
      margin-bottom: 0.75rem;
      padding: 0 0.5rem;
    }
    
    [data-slot="calendar"] .rdp-weekday {
      font-size: 0.75rem;
      padding: 0.5rem 0.125rem;
      text-align: center;
      width: calc(100% / 7);
      min-width: calc(280px / 7);
    }
    
    [data-slot="calendar"] button {
      font-size: 0.8rem !important;
      height: calc(280px / 7 - 4px) !important;
      width: calc(280px / 7 - 4px) !important;
      min-height: calc(280px / 7 - 4px) !important;
      min-width: calc(280px / 7 - 4px) !important;
      max-height: 2.25rem !important;
      max-width: 2.25rem !important;
      padding: 0 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      margin: 0.125rem auto !important;
      border-radius: 0.375rem !important;
    }
    
    /* Navigation buttons */
    [data-slot="calendar"] [data-testid="nav-button"] {
      height: 1.75rem !important;
      width: 1.75rem !important;
      min-height: 1.75rem !important;
      min-width: 1.75rem !important;
    }
    
    /* Prevent calendar overflow on mobile */
    [data-slot="calendar"] .rdp-months {
      gap: 0.5rem;
      width: 100%;
    }
    
    [data-slot="calendar"] .rdp-month {
      width: 100%;
      max-width: 100%;
      overflow: visible;
    }
    
    [data-slot="calendar"] .rdp-table {
      width: 100%;
      table-layout: fixed;
      border-spacing: 2px;
      margin-top: 0.5rem;
    }
    
    [data-slot="calendar"] .rdp-weekdays {
      width: 100%;
      display: flex;
      margin-bottom: 0.25rem;
    }
    
    [data-slot="calendar"] .rdp-week {
      width: 100%;
      display: flex;
      margin-bottom: 0.125rem;
    }
    
    [data-slot="calendar"] .rdp-day {
      width: calc(100% / 7);
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0.125rem;
      min-width: calc(280px / 7);
    }
    
    /* Ensure weekday headers are properly sized */
    [data-slot="calendar"] .rdp-weekday {
      flex: 1;
      min-width: calc(280px / 7);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    /* Mobile popover adjustments */
    [data-radix-popper-content-wrapper] {
      z-index: 50;
      max-width: calc(100vw - 16px);
    }
    
    /* Ensure popover content doesn't overflow on mobile */
    [role="dialog"][data-radix-popper-content-wrapper] {
      max-width: 90vw;
      margin: 0 5vw;
    }
    
    /* Mobile-specific popover sizing */
    [data-radix-popover-content] {
      width: 12rem !important; /* w-48 equivalent */
      max-width: calc(100vw - 2rem) !important;
    }
  }
}
