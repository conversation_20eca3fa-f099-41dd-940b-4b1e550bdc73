import { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createApiResponse, handleApiError } from '@/lib/api-utils'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return createApiResponse(undefined, 'Email and password are required', 400)
    }

    const supabase = await createClient()

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      return createApiResponse(undefined, error.message, 401)
    }

    return createApiResponse({
      user: data.user,
      session: data.session
    })
  } catch (error) {
    return handleApiError(error)
  }
}
