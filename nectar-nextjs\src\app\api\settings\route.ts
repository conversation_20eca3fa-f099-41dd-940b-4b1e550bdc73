import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import { resolveHealthcareProfessionalId } from '@/lib/clinic-settings-utils'
import { debugLog } from '@/lib/debug-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if a specific user_id or healthcare_professional_id is provided (for admin/secretary access)
      const url = new URL(request.url)
      const targetUserId = url.searchParams.get('user_id')
      const healthcareProfessionalId = url.searchParams.get('healthcare_professional_id')

      let finalUserId = userId;

      debugLog.info('⚙️ Fetching settings:', {
        currentUserId: userId,
        targetUserId,
        healthcareProfessionalId
      });

      // Get current user's role for permission checking
      const { data: currentUser, error: currentUserError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (currentUserError) {
        debugLog.error('❌ Error fetching current user role:', currentUserError);
        return handleApiError(new Error('Failed to verify user permissions'))
      }

      // If healthcare_professional_id is provided, get the corresponding user_id
      if (healthcareProfessionalId) {
        const { data: hpData, error: hpError } = await supabase
          .from('healthcare_professionals')
          .select('user_id')
          .eq('id', healthcareProfessionalId)
          .single()

        if (hpError) {
          debugLog.error('❌ Error finding healthcare professional by ID:', hpError);
          return handleApiError(new Error('Healthcare professional not found'))
        }

        finalUserId = hpData.user_id;
        debugLog.info('✅ Found user_id for healthcare_professional_id:', { 
          healthcareProfessionalId, 
          userId: finalUserId 
        });

        // If user is a secretary, check if they have access to this healthcare professional
        if (currentUser.role === 'secretary' && finalUserId !== userId) {
          const { data: association, error: associationError } = await supabase
            .from('user_associations')
            .select('id')
            .eq('accessor_user_id', userId)
            .eq('target_user_id', finalUserId)
            .eq('association_type', 'secretary_to_doctor')
            .eq('is_active', true)
            .single()

          if (associationError || !association) {
            debugLog.error('❌ Secretary access denied - no valid association found:', {
              secretaryId: userId,
              targetUserId: finalUserId,
              error: associationError
            });
            return createApiResponse(undefined, 'Access denied: You do not have permission to access this healthcare professional settings', 403)
          }

          debugLog.info('✅ Secretary access validated for healthcare professional');
        }
      } else if (targetUserId) {
        finalUserId = targetUserId;
        
        // If user is a secretary trying to access another user's settings, validate access
        if (currentUser.role === 'secretary' && finalUserId !== userId) {
          const { data: association, error: associationError } = await supabase
            .from('user_associations')
            .select('id')
            .eq('accessor_user_id', userId)
            .eq('target_user_id', finalUserId)
            .eq('association_type', 'secretary_to_doctor')
            .eq('is_active', true)
            .single()

          if (associationError || !association) {
            debugLog.error('❌ Secretary access denied - no valid association found for target user');
            return createApiResponse(undefined, 'Access denied: You do not have permission to access this user settings', 403)
          }
        }
      }

      debugLog.info('⚙️ Fetching settings for user:', finalUserId);

      // Get user profile information
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', finalUserId)
        .single()

      if (userError) {
        debugLog.error('❌ Error fetching user data:', userError)
        return handleApiError(userError)
      }

      // Get healthcare professional data if user is a healthcare professional
      let healthcareProfessionalData = null
      if (userData?.role === 'healthcare_professional') {
        const { data, error } = await supabase
          .from('healthcare_professionals')
          .select('*')
          .eq('user_id', finalUserId)
          .single()

        if (error) {
          debugLog.error('⚠️ Error fetching healthcare professional data (user may not have a record):', error)
          // Don't return error - user might not have a healthcare professional record yet
        } else {
          healthcareProfessionalData = data
          debugLog.info('✅ Found healthcare professional data for user');
        }
      }

      const profile = healthcareProfessionalData ? {
        name: healthcareProfessionalData.name || '',
        email: healthcareProfessionalData.email || userData?.email || '',
        phone: healthcareProfessionalData.phone || '',
        specialty: healthcareProfessionalData.specialty || '',
        crm: healthcareProfessionalData.crm || '',
        address: healthcareProfessionalData.address || '',
        cpf: healthcareProfessionalData.cpf || ''
      } : {
        name: userData?.name || '',
        email: userData?.email || '',
        phone: userData?.phone || '',
        specialty: '',
        crm: '',
        address: '',
        cpf: ''
      }

      const settings = {
        profile,
        notifications: {
          email_appointments: true,
          sms_reminders: false,
          push_notifications: true,
          marketing_emails: false
        },
        integrations: {
          whatsapp_token: '',
          whatsapp_phone: '',
          email_smtp_host: '',
          email_smtp_port: '',
          email_smtp_user: '',
          email_smtp_password: ''
        }
      }

      debugLog.info('✅ Successfully returning settings for user:', finalUserId);
      return createApiResponse(settings)
    } catch (error) {
      debugLog.error('❌ Settings API error:', error);
      return handleApiError(error)
    }
  })
}
