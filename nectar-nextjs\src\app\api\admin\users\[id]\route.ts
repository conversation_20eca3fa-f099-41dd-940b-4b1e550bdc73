import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user is admin using simplified role system
      const { data: currentUser, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (userError) {
        return handleApiError(userError)
      }

      const isAdmin = currentUser?.role === 'admin'
      if (!isAdmin) {
        return createApiResponse(null, 'Forbidden', 403)
      }

      const { data: user, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', params.id)
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(user)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user is admin using simplified role system
      const { data: currentUser, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (userError) {
        return handleApiError(userError)
      }

      const isAdmin = currentUser?.role === 'admin'
      if (!isAdmin) {
        return createApiResponse(null, 'Forbidden', 403)
      }

      const body = await request.json()
      
      const { data: user, error } = await supabase
        .from('users')
        .update(body)
        .eq('id', params.id)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(user, 'User updated successfully')
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user is admin using simplified role system
      const { data: currentUser, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (userError) {
        return handleApiError(userError)
      }

      const isAdmin = currentUser?.role === 'admin'
      if (!isAdmin) {
        return createApiResponse(null, 'Forbidden', 403)
      }

      // Don't allow deleting yourself
      if (params.id === userId) {
        return createApiResponse(null, 'Cannot delete yourself', 400)
      }

      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', params.id)

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(null, 'User deleted successfully')
    } catch (error) {
      return handleApiError(error)
    }
  })
}
