/**
 * Time Slots Generation and Management
 * 
 * This module handles the generation and display of appointment time slots with support
 * for dynamic subdivision when multiple appointments exist within the same time period.
 * 
 * Key Features:
 * - Automatic slot subdivision for multiple appointments
 * - Conflict detection and visual indicators
 * - Preserves clinic settings for default slot duration
 * - Responsive visual hierarchy for subdivided slots
 * 
 * <AUTHOR> Management System
 * @version 2.0.0 - Added multi-appointment support
 */

import { addMinutes, format, isSameDay, isWithinInterval, startOfDay, endOfDay } from 'date-fns';

// Conditional debug logging
const debugLog = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(message, data);
    }
  }
};

export type TimeSlot = {
  id: string;
  start_time: Date;
  end_time: Date;
  isAvailable: boolean;
  isBlocked: boolean;
  type: 'available' | 'appointment' | 'blocked';
  appointment?: any;
  block?: any;
  // New properties for multi-appointment support
  appointments?: any[]; // Array of appointments in this slot
  isSubdivided?: boolean; // Flag to indicate if this slot contains multiple appointments
  parentSlotId?: string; // Reference to the original slot if this is a subdivision
};

export type WorkingDay = {
  enabled: boolean;
  working_hours_start: string;
  working_hours_end: string;
  break_intervals: { start: string; end: string; }[];
};

export type ClinicSettings = {
  appointment_duration_minutes: number;
  timezone: string;
  weekly_schedule: {
    monday: WorkingDay;
    tuesday: WorkingDay;
    wednesday: WorkingDay;
    thursday: WorkingDay;
    friday: WorkingDay;
    saturday: WorkingDay;
    sunday: WorkingDay;
  };
};

// Legacy format for backward compatibility
export type LegacyClinicSettings = {
  working_hours_start: string;
  working_hours_end: string;
  working_days: number[];
  appointment_duration_minutes: number;
  allow_weekend_appointments: boolean;
};

/**
 * Find all appointments that overlap with a given time range
 */
function findOverlappingAppointments(
  slotStart: Date,
  slotEnd: Date,
  appointments: any[]
): any[] {
  return appointments.filter(apt => {
    if (apt.status === 'cancelled') return false;
    
    const aptStart = new Date(apt.start_time);
    const aptEnd = new Date(apt.end_time);
    
    return (
      (aptStart >= slotStart && aptStart < slotEnd) ||
      (aptEnd > slotStart && aptEnd <= slotEnd) ||
      (aptStart <= slotStart && aptEnd >= slotEnd)
    );
  });
}

/**
 * Check if multiple appointments have actual overlapping times (conflict scenario)
 */
function hasAppointmentConflicts(appointments: any[]): boolean {
  if (appointments.length <= 1) return false;
  
  const sortedAppointments = [...appointments].sort((a, b) => 
    new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
  );
  
  for (let i = 0; i < sortedAppointments.length - 1; i++) {
    const current = sortedAppointments[i];
    const next = sortedAppointments[i + 1];
    
    const currentEnd = new Date(current.end_time);
    const nextStart = new Date(next.start_time);
    
    // If next appointment starts before current one ends, there's a conflict
    if (nextStart < currentEnd) {
      return true;
    }
  }
  
  return false;
}

/**
 * Subdivide a time slot to accommodate multiple appointments
 */
function subdivideSlot(
  slotStart: Date,
  slotEnd: Date,
  appointments: any[],
  slotId: string
): TimeSlot[] {
  const subSlots: TimeSlot[] = [];
  
  // Check if there are actual conflicts between appointments
  const hasConflicts = hasAppointmentConflicts(appointments);
  
  if (hasConflicts) {
    // If there are conflicts, show all appointments in the slot with conflict indicators
    appointments.forEach((apt, index) => {
      const aptStart = new Date(apt.start_time);
      const aptEnd = new Date(apt.end_time);
      
      // Ensure appointment times are within slot bounds
      const effectiveAptStart = aptStart < slotStart ? slotStart : aptStart;
      const effectiveAptEnd = aptEnd > slotEnd ? slotEnd : aptEnd;
      
      subSlots.push({
        id: `${slotId}-conflict-apt-${apt.id}`,
        start_time: new Date(effectiveAptStart),
        end_time: new Date(effectiveAptEnd),
        isAvailable: false,
        isBlocked: false,
        type: 'appointment',
        appointment: { ...apt, hasConflict: true },
        parentSlotId: slotId,
        isSubdivided: true
      });
    });
    
    return subSlots;
  }
  
  // No conflicts - create sequential subdivision
  const sortedAppointments = [...appointments].sort((a, b) => 
    new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
  );
  
  let currentTime = new Date(slotStart);
  
  for (let i = 0; i < sortedAppointments.length; i++) {
    const apt = sortedAppointments[i];
    const aptStart = new Date(apt.start_time);
    const aptEnd = new Date(apt.end_time);
    
    // Ensure appointment times are within slot bounds
    const effectiveAptStart = aptStart < slotStart ? slotStart : aptStart;
    const effectiveAptEnd = aptEnd > slotEnd ? slotEnd : aptEnd;
    
    // Create available slot before appointment if there's a gap
    if (currentTime < effectiveAptStart) {
      const gapDuration = effectiveAptStart.getTime() - currentTime.getTime();
      // Only create gap slot if it's at least 5 minutes (300000 ms)
      if (gapDuration >= 300000) {
        subSlots.push({
          id: `${slotId}-available-${i}`,
          start_time: new Date(currentTime),
          end_time: new Date(effectiveAptStart),
          isAvailable: true,
          isBlocked: false,
          type: 'available',
          parentSlotId: slotId,
          isSubdivided: true
        });
      }
    }
    
    // Create appointment slot
    subSlots.push({
      id: `${slotId}-apt-${apt.id}`,
      start_time: new Date(effectiveAptStart),
      end_time: new Date(effectiveAptEnd),
      isAvailable: false,
      isBlocked: false,
      type: 'appointment',
      appointment: apt,
      parentSlotId: slotId,
      isSubdivided: true
    });
    
    currentTime = new Date(effectiveAptEnd);
  }
  
  // Create available slot after all appointments if there's remaining time
  if (currentTime < slotEnd) {
    const remainingDuration = slotEnd.getTime() - currentTime.getTime();
    // Only create remaining slot if it's at least 5 minutes (300000 ms)
    if (remainingDuration >= 300000) {
      subSlots.push({
        id: `${slotId}-available-end`,
        start_time: new Date(currentTime),
        end_time: new Date(slotEnd),
        isAvailable: true,
        isBlocked: false,
        type: 'available',
        parentSlotId: slotId,
        isSubdivided: true
      });
    }
  }
  
  return subSlots;
}

/**
 * Generate time slots for a specific date based on clinic settings
 * 
 * This function creates time slots based on the clinic's default appointment duration,
 * but automatically subdivides slots when multiple appointments exist within the same
 * time period. This ensures all appointments are visible in the UI.
 * 
 * Features:
 * - Dynamic slot subdivision for multiple appointments
 * - Conflict detection for overlapping appointments
 * - Gap handling for available time between appointments
 * - Visual indicators for subdivided and conflicted slots
 */
export function generateTimeSlots(
  date: Date,
  clinicSettings: ClinicSettings | LegacyClinicSettings,
  appointments: any[] = [],
  blocks: any[] = []
): TimeSlot[] {
  const slots: TimeSlot[] = [];
  
  try {
    // Ensure appointments and blocks are arrays
    const validAppointments = Array.isArray(appointments) ? appointments : [];
    const validBlocks = Array.isArray(blocks) ? blocks : [];
    
    // Validate date input
    if (!date || isNaN(date.getTime())) {
      debugLog.info('⚠️ Invalid date provided to generateTimeSlots');
      return slots;
    }
  
  // Check if we're using the new format or legacy format
  const isNewFormat = 'weekly_schedule' in clinicSettings;
  
  let dayConfig: WorkingDay | null = null;
  let appointmentDuration = 30;
  
  if (isNewFormat) {
    const newSettings = clinicSettings as ClinicSettings;
    appointmentDuration = newSettings.appointment_duration_minutes || 30;
    
    // Get day configuration
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayOfWeek = date.getDay();
    const dayName = dayNames[dayOfWeek] as keyof typeof newSettings.weekly_schedule;
    
    dayConfig = newSettings.weekly_schedule[dayName];
    
    // If day is not enabled, return empty slots
    if (!dayConfig || !dayConfig.enabled) {
      return slots;
    }
  } else {
    // Legacy format
    const legacySettings = clinicSettings as LegacyClinicSettings;
    appointmentDuration = legacySettings.appointment_duration_minutes || 30;
    
    // Check if the day is within working days (legacy format)
    const dayOfWeek = date.getDay();
    const workingDays = Array.isArray(legacySettings.working_days) ? legacySettings.working_days : [1, 2, 3, 4, 5];
    
    if (!legacySettings.allow_weekend_appointments && !workingDays.includes(dayOfWeek)) {
      return slots;
    }
    
    // Convert legacy format to day config
    dayConfig = {
      enabled: true,
      working_hours_start: legacySettings.working_hours_start || '08:00',
      working_hours_end: legacySettings.working_hours_end || '18:00',
      break_intervals: []
    };
  }
  
  if (!dayConfig) return slots;

  // Parse working hours
  const [startHour, startMinute] = dayConfig.working_hours_start.split(':').map(Number);
  const [endHour, endMinute] = dayConfig.working_hours_end.split(':').map(Number);

  // Create start and end times for the day
  const workingStart = new Date(date);
  workingStart.setHours(startHour, startMinute, 0, 0);
  
  const workingEnd = new Date(date);
  workingEnd.setHours(endHour, endMinute, 0, 0);

  // Generate slots based on appointment duration
  let currentTime = new Date(workingStart);
  
  while (currentTime < workingEnd) {
    const slotEnd = addMinutes(currentTime, appointmentDuration);
    
    // Don't create slot if it would extend beyond working hours
    if (slotEnd > workingEnd) {
      break;
    }

    // Check if this slot is within a break interval
    const isInBreakInterval = dayConfig.break_intervals.some(breakInterval => {
      const [breakStartHour, breakStartMinute] = breakInterval.start.split(':').map(Number);
      const [breakEndHour, breakEndMinute] = breakInterval.end.split(':').map(Number);
      
      const breakStart = new Date(date);
      breakStart.setHours(breakStartHour, breakStartMinute, 0, 0);
      
      const breakEnd = new Date(date);
      breakEnd.setHours(breakEndHour, breakEndMinute, 0, 0);
      
      return (
        (currentTime >= breakStart && currentTime < breakEnd) ||
        (slotEnd > breakStart && slotEnd <= breakEnd) ||
        (currentTime <= breakStart && slotEnd >= breakEnd)
      );
    });

    // Skip this slot if it's within a break interval
    if (isInBreakInterval) {
      currentTime = addMinutes(currentTime, appointmentDuration);
      continue;
    }

    // Check if this slot overlaps with any existing appointments
    const overlappingAppointments = findOverlappingAppointments(currentTime, slotEnd, validAppointments);

    // Check if this slot overlaps with any blocked time
    const overlappingBlock = validBlocks.find(block => {
      const blockStart = new Date(block.start_time);
      const blockEnd = new Date(block.end_time);
      
      return (
        (currentTime >= blockStart && currentTime < blockEnd) ||
        (slotEnd > blockStart && slotEnd <= blockEnd) ||
        (currentTime <= blockStart && slotEnd >= blockEnd)
      );
    });

    // Handle slot creation based on overlapping content
    if (overlappingBlock) {
      // Blocked slot - simple case, no subdivision needed
      const slot: TimeSlot = {
        id: `slot-${currentTime.getTime()}`,
        start_time: new Date(currentTime),
        end_time: new Date(slotEnd),
        isAvailable: false,
        isBlocked: true,
        type: 'blocked',
        block: overlappingBlock
      };
      slots.push(slot);
    } else if (overlappingAppointments.length === 0) {
      // Available slot - no subdivision needed
      const slot: TimeSlot = {
        id: `slot-${currentTime.getTime()}`,
        start_time: new Date(currentTime),
        end_time: new Date(slotEnd),
        isAvailable: true,
        isBlocked: false,
        type: 'available'
      };
      slots.push(slot);
    } else if (overlappingAppointments.length === 1) {
      // Single appointment - check if it matches the slot duration exactly
      const appointment = overlappingAppointments[0];
      const aptStart = new Date(appointment.start_time);
      const aptEnd = new Date(appointment.end_time);
      const aptDuration = Math.round((aptEnd.getTime() - aptStart.getTime()) / (1000 * 60)); // in minutes
      const slotDuration = appointmentDuration; // standard slot duration in minutes
      
      // Check if appointment starts at slot beginning and has exact duration
      const startsAtSlotBeginning = aptStart.getTime() === currentTime.getTime();
      const hasExactDuration = aptDuration === slotDuration;
      
      if (startsAtSlotBeginning && hasExactDuration) {
        // Simple appointment that matches slot exactly - no subdivision needed
        const slot: TimeSlot = {
          id: `slot-${currentTime.getTime()}`,
          start_time: new Date(currentTime),
          end_time: new Date(slotEnd),
          isAvailable: false,
          isBlocked: false,
          type: 'appointment',
          appointment: appointment,
          isSubdivided: false
        };
        slots.push(slot);
      } else {
        // Appointment doesn't match slot exactly - needs subdivision
        const slotId = `slot-${currentTime.getTime()}`;
        debugLog.info(`🔧 Subdividing single appointment slot ${slotId} (custom duration)`, {
          slotStart: currentTime.toISOString(),
          slotEnd: slotEnd.toISOString(),
          appointment: {
            id: appointment.id,
            patient: appointment.patient_name,
            start: appointment.start_time,
            end: appointment.end_time,
            duration: aptDuration,
            expectedDuration: slotDuration
          }
        });
        
        const subSlots = subdivideSlot(currentTime, slotEnd, overlappingAppointments, slotId);
        debugLog.info(`✅ Created ${subSlots.length} sub-slots for single appointment`, subSlots.map(s => ({
          id: s.id,
          type: s.type,
          start: s.start_time.toISOString(),
          end: s.end_time.toISOString(),
          patient: s.appointment?.patient_name
        })));
        
        slots.push(...subSlots);
      }
    } else {
      // Multiple appointments - subdivide the slot
      const slotId = `slot-${currentTime.getTime()}`;
      debugLog.info(`🔧 Subdividing slot ${slotId}: ${overlappingAppointments.length} appointments found`, {
        slotStart: currentTime.toISOString(),
        slotEnd: slotEnd.toISOString(),
        appointments: overlappingAppointments.map(apt => ({
          id: apt.id,
          patient: apt.patient_name,
          start: apt.start_time,
          end: apt.end_time
        }))
      });
      
      const subSlots = subdivideSlot(currentTime, slotEnd, overlappingAppointments, slotId);
      debugLog.info(`✅ Created ${subSlots.length} sub-slots`, subSlots.map(s => ({
        id: s.id,
        type: s.type,
        start: s.start_time.toISOString(),
        end: s.end_time.toISOString(),
        patient: s.appointment?.patient_name
      })));
      
      slots.push(...subSlots);
    }

    // Move to next slot
    currentTime = addMinutes(currentTime, appointmentDuration);
  }

  return slots;
  } catch (error) {
    debugLog.info('❌ Error generating time slots:', error);
    return [];
  }
}

/**
 * Check if a time slot is available for booking
 */
export function isSlotAvailable(
  startTime: Date,
  endTime: Date,
  appointments: any[] = [],
  blocks: any[] = [],
  clinicSettings?: ClinicSettings | LegacyClinicSettings
): boolean {
  // Ensure appointments and blocks are arrays
  const validAppointments = Array.isArray(appointments) ? appointments : [];
  const validBlocks = Array.isArray(blocks) ? blocks : [];
  
  // Check if the time is within working hours and not in break intervals
  if (clinicSettings && 'weekly_schedule' in clinicSettings) {
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayOfWeek = startTime.getDay();
    const dayName = dayNames[dayOfWeek] as keyof typeof clinicSettings.weekly_schedule;
    const dayConfig = clinicSettings.weekly_schedule[dayName];
    
    // Check if day is enabled
    if (!dayConfig || !dayConfig.enabled) {
      return false;
    }
    
    // Check if time is within working hours
    const [startHour, startMinute] = dayConfig.working_hours_start.split(':').map(Number);
    const [endHour, endMinute] = dayConfig.working_hours_end.split(':').map(Number);
    
    const workingStart = new Date(startTime);
    workingStart.setHours(startHour, startMinute, 0, 0);
    
    const workingEnd = new Date(startTime);
    workingEnd.setHours(endHour, endMinute, 0, 0);
    
    if (startTime < workingStart || endTime > workingEnd) {
      return false;
    }
    
    // Check if time overlaps with break intervals
    const isInBreakInterval = dayConfig.break_intervals.some(breakInterval => {
      const [breakStartHour, breakStartMinute] = breakInterval.start.split(':').map(Number);
      const [breakEndHour, breakEndMinute] = breakInterval.end.split(':').map(Number);
      
      const breakStart = new Date(startTime);
      breakStart.setHours(breakStartHour, breakStartMinute, 0, 0);
      
      const breakEnd = new Date(startTime);
      breakEnd.setHours(breakEndHour, breakEndMinute, 0, 0);
      
      return (
        (startTime >= breakStart && startTime < breakEnd) ||
        (endTime > breakStart && endTime <= breakEnd) ||
        (startTime <= breakStart && endTime >= breakEnd)
      );
    });
    
    if (isInBreakInterval) {
      return false;
    }
  }
  
  // Check appointments
  const hasAppointmentConflict = validAppointments.some(apt => {
    if (apt.status === 'cancelled') return false;
    
    const aptStart = new Date(apt.start_time);
    const aptEnd = new Date(apt.end_time);
    
    return (
      (startTime >= aptStart && startTime < aptEnd) ||
      (endTime > aptStart && endTime <= aptEnd) ||
      (startTime <= aptStart && endTime >= aptEnd)
    );
  });

  // Check blocks
  const hasBlockConflict = validBlocks.some(block => {
    const blockStart = new Date(block.start_time);
    const blockEnd = new Date(block.end_time);
    
    return (
      (startTime >= blockStart && startTime < blockEnd) ||
      (endTime > blockStart && endTime <= blockEnd) ||
      (startTime <= blockStart && endTime >= blockEnd)
    );
  });

  return !hasAppointmentConflict && !hasBlockConflict;
}

/**
 * Format time for display in time slots
 */
export function formatSlotTime(date: Date): string {
  return format(date, 'HH:mm');
}

/**
 * Get slot duration in minutes
 */
export function getSlotDuration(slot: TimeSlot): number {
  return Math.round((slot.end_time.getTime() - slot.start_time.getTime()) / (1000 * 60));
}

/**
 * Check if a slot is subdivided (contains multiple appointments or partial availability)
 */
export function isSlotSubdivided(slot: TimeSlot): boolean {
  return slot.isSubdivided === true;
}

/**
 * Get all appointments within a time range (for subdivision analysis)
 */
export function getAppointmentsInTimeRange(
  startTime: Date,
  endTime: Date,
  appointments: any[]
): any[] {
  return findOverlappingAppointments(startTime, endTime, appointments);
}
