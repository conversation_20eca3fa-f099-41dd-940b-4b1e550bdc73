import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const date = searchParams.get('date')
      const startDate = searchParams.get('start_date')
      const endDate = searchParams.get('end_date')
      const healthcareProfessionalId = searchParams.get('healthcare_professional_id')

      let query = supabase
        .from('appointment_blocks')
        .select(`
          *,
          healthcare_professionals(name, specialty)
        `)
        .order('start_time', { ascending: true })

      // Filter by healthcare professional if specified
      if (healthcareProfessionalId) {
        query = query.eq('healthcare_professional_id', healthcareProfessionalId)
      }

      // Filter by date range if specified
      if (startDate && endDate) {
        const start = new Date(startDate + 'T00:00:00.000Z')
        const end = new Date(endDate + 'T23:59:59.999Z')
        
        query = query
          .gte('start_time', start.toISOString())
          .lte('start_time', end.toISOString())
      } else if (date) {
        // Filter by single date if specified
        const startOfDay = new Date(date + 'T00:00:00.000Z')
        const endOfDay = new Date(date + 'T23:59:59.999Z')
        
        query = query
          .gte('start_time', startOfDay.toISOString())
          .lte('start_time', endOfDay.toISOString())
      }

      const { data: blocks, error } = await query

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(blocks || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const startTime = new Date(body.start_time).toISOString()
      const endTime = new Date(body.end_time).toISOString()
      const healthcareProfessionalId = body.healthcare_professional_id || null

      // Check for existing appointments in this time slot
      if (healthcareProfessionalId) {
        // Simple overlap detection: any appointment that overlaps with our time range
        const { data: existingAppointments } = await supabase
          .from('appointments')
          .select('id, start_time, end_time, status')
          .eq('healthcare_professional_id', healthcareProfessionalId)
          .neq('status', 'cancelled')
          .lt('start_time', endTime)
          .gt('end_time', startTime)

        if (existingAppointments && existingAppointments.length > 0) {
          return createApiResponse(
            null, 
            'Já existe uma consulta marcada neste horário. Não é possível bloquear.',
            409
          )
        }

        // Check for existing blocks in this time slot
        const { data: existingBlocks } = await supabase
          .from('appointment_blocks')
          .select('id, start_time, end_time')
          .eq('healthcare_professional_id', healthcareProfessionalId)
          .lt('start_time', endTime)
          .gt('end_time', startTime)

        if (existingBlocks && existingBlocks.length > 0) {
          return createApiResponse(
            null, 
            'Já existe um bloqueio neste horário.',
            409
          )
        }
      }
      
      const blockData = {
        start_time: startTime,
        end_time: endTime,
        healthcare_professional_id: healthcareProfessionalId,
        created_by: userId,
        reason: body.reason || 'Horário bloqueado pelo médico'
      }

      const { data: block, error } = await supabase
        .from('appointment_blocks')
        .insert(blockData)
        .select(`
          *,
          healthcare_professionals(name, specialty)
        `)
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(block, 'Horário bloqueado com sucesso', 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
