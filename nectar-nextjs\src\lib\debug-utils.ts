// Debug utility for conditional logging
const DEBUG_MODE = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_DEBUG === 'true';

export const debugLog = {
  info: (...args: any[]) => {
    if (DEBUG_MODE) {
      console.log(...args);
    }
  },
  error: (...args: any[]) => {
    if (DEBUG_MODE) {
      console.error(...args);
    }
  },
  warn: (...args: any[]) => {
    if (DEBUG_MODE) {
      console.warn(...args);
    }
  },
  // Always log critical errors
  critical: (...args: any[]) => {
    console.error(...args);
  }
};

// Performance monitoring utility
export const perfMon = {
  mark: (name: string) => {
    if (DEBUG_MODE && performance.mark) {
      performance.mark(name);
    }
  },
  measure: (name: string, startMark: string, endMark?: string) => {
    if (DEBUG_MODE && performance.measure) {
      try {
        performance.measure(name, startMark, endMark);
        const measures = performance.getEntriesByName(name);
        const lastMeasure = measures[measures.length - 1];
        console.log(`⏱️ ${name}: ${lastMeasure.duration.toFixed(2)}ms`);
      } catch (e) {
        // Ignore performance measurement errors
      }
    }
  }
};
