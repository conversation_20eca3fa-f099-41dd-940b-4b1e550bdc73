"use client"

import { useState, useEffect } from 'react'
import { makeAuthenticatedRequest } from '@/lib/api-client'

interface UserProfile {
  id: string
  email: string
  name: string
  phone?: string
  role: string
  is_active: boolean
  created_at: string
}

interface UseUserProfileReturn {
  profile: UserProfile | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  updateProfile: (data: Partial<Pick<UserProfile, 'name' | 'phone'>>) => Promise<boolean>
}

export function useUserProfile(): UseUserProfileReturn {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProfile = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await makeAuthenticatedRequest('/api/profile')
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to fetch user profile')
      }

      const result = await response.json()
      setProfile(result.data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Error fetching user profile:', err)
    } finally {
      setLoading(false)
    }
  }

  const updateProfile = async (data: Partial<Pick<UserProfile, 'name' | 'phone'>>): Promise<boolean> => {
    try {
      const response = await makeAuthenticatedRequest('/api/profile', {
        method: 'PATCH',
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to update profile')
      }

      const result = await response.json()
      setProfile(result.data)
      return true
    } catch (err) {
      console.error('Error updating profile:', err)
      return false
    }
  }

  useEffect(() => {
    fetchProfile()
  }, [])

  return {
    profile,
    loading,
    error,
    refetch: fetchProfile,
    updateProfile
  }
}
