import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

interface AccessibleUser {
  user_id: string
  email: string
  name: string
  role: string
  association_type: string | null
  permissions: Record<string, string[]> | null
  is_own_data: boolean
}

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const roleFilter = searchParams.get('role')
      const includeInactive = searchParams.get('include_inactive') === 'true'

      // Use the database function to get accessible users
      const { data: accessibleUsers, error } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      if (error) {
        return handleApiError(error)
      }

      let filteredUsers = accessibleUsers || []

      // Apply role filter if specified
      if (roleFilter) {
        filteredUsers = filteredUsers.filter((user: AccessibleUser) => user.role === roleFilter)
      }

      // Get additional user details and filter by active status
      const userIds = filteredUsers.map((user: AccessibleUser) => user.user_id)
      
      if (userIds.length === 0) {
        return createApiResponse([])
      }

      const { data: userDetails, error: detailsError } = await supabase
        .from('users')
        .select('id, email, name, phone, role, is_active, created_at')
        .in('id', userIds)
        .eq('is_active', includeInactive ? undefined : true)

      if (detailsError) {
        return handleApiError(detailsError)
      }

      // Merge the data
      const enrichedUsers = filteredUsers.map((accessibleUser: AccessibleUser) => {
        const userDetail = userDetails?.find(detail => detail.id === accessibleUser.user_id)
        return {
          ...accessibleUser,
          ...userDetail,
          user_id: accessibleUser.user_id, // Keep the original field name
        }
      }).filter(user => includeInactive || user.is_active)

      // Sort by name
      enrichedUsers.sort((a, b) => {
        if (a.is_own_data && !b.is_own_data) return -1
        if (!a.is_own_data && b.is_own_data) return 1
        return (a.name || '').localeCompare(b.name || '')
      })

      return createApiResponse(enrichedUsers)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

// POST endpoint to check specific access permissions
export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      const { target_user_id, resource, action } = body

      if (!target_user_id) {
        return createApiResponse(null, 'target_user_id is required', 400)
      }

      // Use the database function to check access
      const { data: hasAccess, error } = await supabase
        .rpc('has_access_to_user', {
          target_user_id,
          required_resource: resource || null,
          required_action: action || null
        })

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse({
        target_user_id,
        resource,
        action,
        has_access: hasAccess
      })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
