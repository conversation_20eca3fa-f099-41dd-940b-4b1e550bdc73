import { 
  patientSchema, 
  appointmentSchema, 
  validateCPF, 
  formatCPF, 
  formatPhone 
} from '@/lib/validations'

describe('Validations', () => {
  describe('patientSchema', () => {
    it('should validate a valid patient', () => {
      const validPatient = {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '(11) 99999-9999',
        birth_date: '1990-01-01',
        cpf: '123.456.789-00',
        address: 'Rua das Flores, 123',
        notes: 'Paciente regular'
      }

      const result = patientSchema.safeParse(validPatient)
      expect(result.success).toBe(true)
    })

    it('should reject invalid name', () => {
      const invalidPatient = {
        name: 'A', // Too short
        email: '<EMAIL>'
      }

      const result = patientSchema.safeParse(invalidPatient)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('pelo menos 2 caracteres')
      }
    })

    it('should reject invalid email', () => {
      const invalidPatient = {
        name: '<PERSON>',
        email: 'invalid-email'
      }

      const result = patientSchema.safeParse(invalidPatient)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('E-mail inválido')
      }
    })

    it('should reject invalid phone', () => {
      const invalidPatient = {
        name: 'João Silva',
        phone: '123'
      }

      const result = patientSchema.safeParse(invalidPatient)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Telefone inválido')
      }
    })

    it('should reject invalid CPF format', () => {
      const invalidPatient = {
        name: 'João Silva',
        cpf: '12345678900' // Missing formatting
      }

      const result = patientSchema.safeParse(invalidPatient)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('CPF inválido')
      }
    })
  })

  describe('appointmentSchema', () => {
    it('should validate a valid appointment', () => {
      const validAppointment = {
        title: 'Consulta - João Silva',
        description: 'Consulta de rotina',
        patient_id: '123e4567-e89b-12d3-a456-426614174000',
        healthcare_professional_id: '123e4567-e89b-12d3-a456-426614174001',
        start_time: '2024-01-15T09:00:00.000Z',
        end_time: '2024-01-15T10:00:00.000Z',
        type: 'consultation' as const,
        notes: 'Paciente em jejum',
        has_recurrence: false
      }

      const result = appointmentSchema.safeParse(validAppointment)
      expect(result.success).toBe(true)
    })

    it('should reject appointment with end time before start time', () => {
      const invalidAppointment = {
        title: 'Consulta - João Silva',
        patient_id: '123e4567-e89b-12d3-a456-426614174000',
        start_time: '2024-01-15T10:00:00.000Z',
        end_time: '2024-01-15T09:00:00.000Z', // Before start time
        type: 'consultation' as const,
        has_recurrence: false
      }

      const result = appointmentSchema.safeParse(invalidAppointment)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('posterior')
      }
    })

    it('should require recurrence fields when has_recurrence is true', () => {
      const invalidAppointment = {
        title: 'Consulta - João Silva',
        patient_id: '123e4567-e89b-12d3-a456-426614174000',
        start_time: '2024-01-15T09:00:00.000Z',
        end_time: '2024-01-15T10:00:00.000Z',
        type: 'consultation' as const,
        has_recurrence: true
        // Missing recurrence_type and recurrence_interval
      }

      const result = appointmentSchema.safeParse(invalidAppointment)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('recorrência')
      }
    })

    it('should validate appointment with recurrence', () => {
      const validAppointment = {
        title: 'Consulta - João Silva',
        patient_id: '123e4567-e89b-12d3-a456-426614174000',
        start_time: '2024-01-15T09:00:00.000Z',
        end_time: '2024-01-15T10:00:00.000Z',
        type: 'consultation' as const,
        has_recurrence: true,
        recurrence_type: 'weekly' as const,
        recurrence_interval: 1,
        recurrence_days: [1, 3, 5],
        recurrence_end_type: 'count' as const,
        recurrence_count: 10
      }

      const result = appointmentSchema.safeParse(validAppointment)
      expect(result.success).toBe(true)
    })
  })

  describe('CPF validation', () => {
    it('should validate correct CPF', () => {
      expect(validateCPF('11144477735')).toBe(true)
      expect(validateCPF('111.444.777-35')).toBe(true)
    })

    it('should reject invalid CPF', () => {
      expect(validateCPF('11111111111')).toBe(false) // All same digits
      expect(validateCPF('123456789')).toBe(false) // Too short
      expect(validateCPF('12345678901')).toBe(false) // Invalid check digits
    })

    it('should format CPF correctly', () => {
      expect(formatCPF('11144477735')).toBe('111.444.777-35')
      expect(formatCPF('111.444.777-35')).toBe('111.444.777-35') // Already formatted
    })
  })

  describe('Phone formatting', () => {
    it('should format 11-digit phone correctly', () => {
      expect(formatPhone('11999999999')).toBe('(11) 99999-9999')
    })

    it('should format 10-digit phone correctly', () => {
      expect(formatPhone('1199999999')).toBe('(11) 9999-9999')
    })

    it('should return original if invalid format', () => {
      expect(formatPhone('123')).toBe('123')
    })
  })
})
