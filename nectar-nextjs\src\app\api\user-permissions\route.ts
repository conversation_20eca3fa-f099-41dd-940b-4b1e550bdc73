import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import { getUserPermissions } from '@/lib/permissions'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const permissions = await getUserPermissions(userId)
      return createApiResponse(permissions)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
