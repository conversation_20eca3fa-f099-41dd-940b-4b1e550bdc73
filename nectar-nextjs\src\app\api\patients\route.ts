import { NextRequest } from 'next/server'
import { withAuth, withAuthAndPermission, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type Patient = Tables<'patients'>
type PatientInsert = TablesInsert<'patients'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      console.log('Fetching patients for user:', userId);

      // Use the new get_accessible_patients function that includes associations
      const { data: patients, error } = await supabase
        .rpc('get_accessible_patients', { current_user_id: userId })

      if (error) {
        console.error('Error fetching accessible patients:', error);
        return handleApiError(error)
      }

      // Sort patients by name
      const sortedPatients = (patients || []).sort((a: any, b: any) =>
        (a.name || '').localeCompare(b.name || '')
      )

      console.log('Found patients:', sortedPatients.length, 'for user:', userId);
      if (sortedPatients.length > 0) {
        console.log('Patient access types:', sortedPatients.reduce((acc: any, p: any) => {
          acc[p.access_type] = (acc[p.access_type] || 0) + 1;
          return acc;
        }, {}));
      }

      return createApiResponse(sortedPatients)
    } catch (error) {
      console.error('API error:', error);
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()

      // Get current user info to check if they are a healthcare professional
      const { data: currentUser, error: userError } = await supabase
        .from('users')
        .select('id, role')
        .eq('id', userId)
        .single()

      if (userError) {
        return handleApiError(userError)
      }

      // Get accessible users to validate target user
      const { data: accessibleUsers, error: accessError } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      if (accessError) {
        return handleApiError(accessError)
      }

      const accessibleUserIds = accessibleUsers?.map((user: any) => user.user_id) || []

      // Determine the target user_id for the patient
      let targetUserId = userId // Default to current user
      let associatedDoctorId: string | null = null

      // Determine professionals to associate
      let professionalIds: string[] = []

      // If current user is a healthcare professional, auto-associate them
      if (currentUser.role === 'healthcare_professional') {
        const { data: healthcareProfessional, error: hpError } = await supabase
          .from('healthcare_professionals')
          .select('id')
          .eq('user_id', userId)
          .single()

        if (!hpError && healthcareProfessional) {
          professionalIds.push(healthcareProfessional.id)
          console.log('🏥 Healthcare professional creating patient, will auto-associate:', healthcareProfessional.id)
        }
      }

      // Handle multiple healthcare professional IDs
      if (body.healthcare_professional_ids && Array.isArray(body.healthcare_professional_ids)) {
        // Validate that all healthcare professionals exist and are accessible
        for (const professionalId of body.healthcare_professional_ids) {
          const { data: healthcareProfessional, error: hpError } = await supabase
            .from('healthcare_professionals')
            .select('id, user_id')
            .eq('id', professionalId)
            .single()

          if (!hpError && healthcareProfessional && accessibleUserIds.includes(healthcareProfessional.user_id)) {
            if (!professionalIds.includes(professionalId)) {
              professionalIds.push(professionalId)
            }
            console.log('🔗 Will create association with healthcare professional:', professionalId)
          }
        }
      } else if (body.healthcare_professional_id) {
        // Support for legacy single professional ID (backwards compatibility)
        const { data: healthcareProfessional, error: hpError } = await supabase
          .from('healthcare_professionals')
          .select('id, user_id')
          .eq('id', body.healthcare_professional_id)
          .single()

        if (!hpError && healthcareProfessional && accessibleUserIds.includes(healthcareProfessional.user_id)) {
          if (!professionalIds.includes(body.healthcare_professional_id)) {
            professionalIds.push(body.healthcare_professional_id)
          }
          console.log('🔗 Will create association with specified healthcare professional:', body.healthcare_professional_id)
        }
      }

      const patientData: PatientInsert = {
        name: body.name,
        email: body.email || null,
        phone: body.phone || null,
        birth_date: body.birth_date || null,
        cpf: body.cpf || null,
        address: body.address || null,
        notes: body.notes || null,
        created_by: targetUserId
      }

      const { data: patient, error } = await supabase
        .from('patients')
        .insert(patientData)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      // Create associations for all specified healthcare professionals
      if (professionalIds.length > 0 && patient) {
        const associations = professionalIds.map(professionalId => ({
          patient_id: patient.id,
          healthcare_professional_id: professionalId,
          created_by: userId
        }))

        const { error: associationError } = await supabase
          .from('patient_healthcare_professional_associations')
          .insert(associations)

        if (associationError) {
          console.error('Error creating patient-healthcare professional associations:', associationError)
          // Don't fail the patient creation, just log the error
        } else {
          console.log('✅ Created patient-healthcare professional associations:', professionalIds.length)
        }
      }

      return createApiResponse(patient, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
