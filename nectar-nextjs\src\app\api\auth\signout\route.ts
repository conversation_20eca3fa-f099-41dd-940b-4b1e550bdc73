import { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createApiResponse, handleApiError } from '@/lib/api-utils'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    const { error } = await supabase.auth.signOut()

    if (error) {
      return createApiResponse(undefined, error.message, 400)
    }

    return createApiResponse({
      message: 'Successfully signed out'
    })
  } catch (error) {
    return handleApiError(error)
  }
}
