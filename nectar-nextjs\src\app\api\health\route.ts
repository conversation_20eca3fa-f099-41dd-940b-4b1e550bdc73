import { NextRequest } from 'next/server'
import { createApiResponse } from '@/lib/api-utils'

export async function GET(request: NextRequest) {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      services: {
        database: 'connected',
        auth: 'active',
        api: 'running'
      }
    }

    return createApiResponse(healthData, 'System is healthy')
  } catch (error) {
    return createApiResponse(
      { status: 'unhealthy', error: error instanceof Error ? error.message : 'Unknown error' },
      'System health check failed',
      500
    )
  }
}
