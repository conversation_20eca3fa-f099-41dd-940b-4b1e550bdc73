"use client"

import * as React from "react"
import {
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "lucide-react"
import { DayButton, DayPicker, getDefaultClassNames } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button, buttonVariants } from "@/components/ui/button"

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  captionLayout = "label",
  buttonVariant = "ghost",
  formatters,
  components,
  ...props
}: React.ComponentProps<typeof DayPicker> & {
  buttonVariant?: React.ComponentProps<typeof Button>["variant"]
}) {
  const defaultClassNames = getDefaultClassNames()

  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn(
        "bg-background group/calendar p-3 [--cell-size:2rem] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent",
        String.raw`rtl:**:[.rdp-button\_next>svg]:rotate-180`,
        String.raw`rtl:**:[.rdp-button\_previous>svg]:rotate-180`,
        className
      )}
      captionLayout={captionLayout}
      formatters={{
        formatMonthDropdown: (date) =>
          date.toLocaleString("default", { month: "short" }),
        ...formatters,
      }}
      classNames={{
        root: cn("w-fit sm:w-full md:w-fit lg:w-full xl:w-fit", defaultClassNames.root),
        months: cn(
          "relative flex flex-col gap-4 md:flex-row",
          defaultClassNames.months
        ),
        month: cn("flex w-full flex-col gap-4", defaultClassNames.month),
        nav: cn(
          "absolute inset-x-0 top-0 flex w-full items-center justify-between gap-1",
          defaultClassNames.nav
        ),
        button_previous: cn(
          buttonVariants({ variant: buttonVariant }),
          "h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50",
          defaultClassNames.button_previous
        ),
        button_next: cn(
          buttonVariants({ variant: buttonVariant }),
          "h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50",
          defaultClassNames.button_next
        ),
        month_caption: cn(
          "flex h-[--cell-size] w-full items-center justify-center px-[--cell-size]",
          defaultClassNames.month_caption
        ),
        dropdowns: cn(
          "flex h-[--cell-size] w-full items-center justify-center gap-1.5 text-sm font-medium",
          defaultClassNames.dropdowns
        ),
        dropdown_root: cn(
          "has-focus:border-ring border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] relative rounded-md border",
          defaultClassNames.dropdown_root
        ),
        dropdown: cn(
          "bg-popover absolute inset-0 opacity-0",
          defaultClassNames.dropdown
        ),
        caption_label: cn(
          "select-none font-medium",
          captionLayout === "label"
            ? "text-sm"
            : "[&>svg]:text-muted-foreground flex h-8 items-center gap-1 rounded-md pl-2 pr-1 text-sm [&>svg]:size-3.5",
          defaultClassNames.caption_label
        ),
        table: "w-full border-collapse",
        weekdays: cn("flex", defaultClassNames.weekdays),
        weekday: cn(
          "text-muted-foreground flex-1 select-none rounded-md text-[0.8rem] font-normal",
          defaultClassNames.weekday
        ),
        week: cn("mt-2 flex w-full", defaultClassNames.week),
        week_number_header: cn(
          "w-[--cell-size] select-none",
          defaultClassNames.week_number_header
        ),
        week_number: cn(
          "text-muted-foreground select-none text-[0.8rem]",
          defaultClassNames.week_number
        ),
        day: cn(
          "group/day relative aspect-square h-full w-full select-none p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md",
          defaultClassNames.day
        ),
        range_start: cn(
          "bg-accent rounded-l-md",
          defaultClassNames.range_start
        ),
        range_middle: cn("rounded-none", defaultClassNames.range_middle),
        range_end: cn("bg-accent rounded-r-md", defaultClassNames.range_end),
        today: cn(
          "bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none",
          defaultClassNames.today
        ),
        outside: cn(
          "text-muted-foreground aria-selected:text-muted-foreground",
          defaultClassNames.outside
        ),
        disabled: cn(
          "text-muted-foreground opacity-50",
          defaultClassNames.disabled
        ),
        hidden: cn("invisible", defaultClassNames.hidden),
        ...classNames,
      }}
      components={{
        Root: ({ className, rootRef, ...props }) => {
          return (
            <div
              data-slot="calendar"
              ref={rootRef}
              className={cn(className)}
              {...props}
            />
          )
        },
        Chevron: ({ className, orientation, ...props }) => {
          if (orientation === "left") {
            return (
              <ChevronLeftIcon className={cn("size-4", className)} {...props} />
            )
          }

          if (orientation === "right") {
            return (
              <ChevronRightIcon
                className={cn("size-4", className)}
                {...props}
              />
            )
          }

          return (
            <ChevronDownIcon className={cn("size-4", className)} {...props} />
          )
        },
        DayButton: CalendarDayButton,
        WeekNumber: ({ children, ...props }) => {
          return (
            <td {...props}>
              <div className="flex size-[--cell-size] items-center justify-center text-center">
                {children}
              </div>
            </td>
          )
        },
        ...components,
      }}
      {...props}
    />
  )
}

function CalendarDayButton({
  className,
  day,
  modifiers,
  ...props
}: React.ComponentProps<typeof DayButton>) {
  const defaultClassNames = getDefaultClassNames()

  const ref = React.useRef<HTMLButtonElement>(null)
  React.useEffect(() => {
    if (modifiers.focused) ref.current?.focus()
  }, [modifiers.focused])

  return (
    <Button
      ref={ref}
      variant="ghost"
      size="icon"
      data-day={day.date.toLocaleDateString()}
      data-selected-single={
        modifiers.selected &&
        !modifiers.range_start &&
        !modifiers.range_end &&
        !modifiers.range_middle
      }
      data-range-start={modifiers.range_start}
      data-range-end={modifiers.range_end}
      data-range-middle={modifiers.range_middle}
      className={cn(
        "data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 flex aspect-square h-auto w-full min-w-[--cell-size] flex-col gap-1 font-normal leading-none data-[range-end=true]:rounded-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] [&>span]:text-xs [&>span]:opacity-70",
        defaultClassNames.day,
        className
      )}
      {...props}
    />
  )
}

export { Calendar, CalendarDayButton }


/* import * as React from "react";
import { DayPicker } from "react-day-picker";
import { ptBR } from "date-fns/locale";
import { format, isAfter, isSameDay, startOfDay } from "date-fns";
import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";

export type CalendarProps = React.ComponentProps<typeof DayPicker> & {
  appointmentCounts?: Record<string, number>;
  clinicSettings?: {
    working_hours_start: string;
    working_hours_end: string;
    working_days: number[];
    appointment_duration_minutes: number;
    allow_weekend_appointments: boolean;
  };
  appointments?: Array<{
    start_time: string;
    end_time: string;
  }>;
};

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  appointmentCounts = {},
  clinicSettings,
  appointments = [],
  ...props
}: CalendarProps) {
  const modifiers = React.useMemo(() => {
    const hasAppointments: Date[] = [];
    const hasAvailability: Date[] = [];
    const today = startOfDay(new Date());

    Object.keys(appointmentCounts).forEach(dateKey => {
      if (appointmentCounts[dateKey] > 0) {
        hasAppointments.push(new Date(dateKey));
      }
    });

    // Calculate availability for future dates only
    if (clinicSettings) {
      const calculateAvailableSlots = (date: Date) => {
        if (!isAfter(date, today) && !isSameDay(date, today)) return 0;

        const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay();

        if (!clinicSettings.working_days.includes(dayOfWeek)) {
          if (!clinicSettings.allow_weekend_appointments) return 0;
        }

        const [startHour, startMinute] = clinicSettings.working_hours_start.split(':').map(Number);
        const [endHour, endMinute] = clinicSettings.working_hours_end.split(':').map(Number);

        const startMinutes = startHour * 60 + startMinute;
        const endMinutes = endHour * 60 + endMinute;
        const totalMinutes = endMinutes - startMinutes;
        const totalSlots = Math.floor(totalMinutes / clinicSettings.appointment_duration_minutes);

        const dateStr = format(date, 'yyyy-MM-dd');
        const dayAppointments = appointments.filter(apt =>
          format(new Date(apt.start_time), 'yyyy-MM-dd') === dateStr
        );

        const occupiedSlots = dayAppointments.length;
        return Math.max(0, totalSlots - occupiedSlots);
      };

      for (let i = 0; i < 60; i++) {
        const checkDate = new Date(today);
        checkDate.setDate(checkDate.getDate() + i);

        if (calculateAvailableSlots(checkDate) > 0) {
          hasAvailability.push(checkDate);
        }
      }
    }

    return { hasAppointments, hasAvailability };
  }, [appointmentCounts, clinicSettings, appointments]);

  const modifiersClassNames = {
    hasAppointments: "has-appointments",
    hasAvailability: "has-availability"
  };

  return (
    <div className={cn("calendar-wrapper", className)}>
      <style jsx>{`
        .calendar-wrapper :global(.rdp-root) {
          --rdp-accent-color: #2563eb;
          --rdp-accent-background-color: #dbeafe;
          --rdp-day-height: 48px;
          --rdp-day-width: 48px;
          --rdp-day_button-border-radius: 8px;
          --rdp-day_button-border: 1px solid transparent;
          --rdp-day_button-height: 46px;
          --rdp-day_button-width: 46px;
          --rdp-selected-border: 2px solid var(--rdp-accent-color);
          --rdp-disabled-opacity: 0.3;
          --rdp-outside-opacity: 0.5;
          --rdp-today-color: var(--rdp-accent-color);
          --rdp-dropdown-gap: 0.5rem;
          --rdp-months-gap: 2rem;
          --rdp-nav_button-disabled-opacity: 0.5;
          --rdp-nav_button-height: 2.25rem;
          --rdp-nav_button-width: 2.25rem;
          --rdp-nav-height: 2.75rem;
          --rdp-range_middle-background-color: var(--rdp-accent-background-color);
          --rdp-range_middle-color: inherit;
          --rdp-range_start-color: white;
          --rdp-range_start-background: linear-gradient(var(--rdp-gradient-direction), transparent 50%, var(--rdp-range_middle-background-color) 50%);
          --rdp-range_start-date-background-color: var(--rdp-accent-color);
          --rdp-range_end-background: linear-gradient(var(--rdp-gradient-direction), var(--rdp-range_middle-background-color) 50%, transparent 50%);
          --rdp-range_end-color: white;
          --rdp-range_end-date-background-color: var(--rdp-accent-color);
          --rdp-week_number-border-radius: 100%;
          --rdp-week_number-border: 2px solid transparent;
          --rdp-week_number-height: var(--rdp-day-height);
          --rdp-week_number-opacity: 0.75;
          --rdp-week_number-width: var(--rdp-day-width);
          --rdp-weeknumber-text-align: center;
          --rdp-weekday-opacity: 0.75;
          --rdp-weekday-padding: 0.75rem 0rem;
          --rdp-weekday-text-align: center;
          --rdp-gradient-direction: 90deg;
          --rdp-animation_duration: 0.3s;
          --rdp-animation_timing: cubic-bezier(0.4, 0, 0.2, 1);
        }


        .calendar-wrapper {
          max-width: 400px;
          margin: 0 auto;
          padding: 24px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }


        .calendar-wrapper :global(.rdp-root[dir="rtl"]) {
          --rdp-gradient-direction: -90deg;
        }

        .calendar-wrapper :global(.rdp-root[data-broadcast-calendar="true"]) {
          --rdp-outside-opacity: unset;
        }

        .calendar-wrapper :global(.rdp-root) {
          position: relative;
          box-sizing: border-box;
        }

        .calendar-wrapper :global(.rdp-root *) {
          box-sizing: border-box;
        }

        .calendar-wrapper :global(.rdp-day) {
          width: var(--rdp-day-width);
          height: var(--rdp-day-height);
          text-align: center;
        }

        .calendar-wrapper :global(.rdp-day_button) {
          background: none;
          padding: 0;
          margin: 0;
          cursor: pointer;
          font: inherit;
          color: inherit;
          justify-content: center;
          align-items: center;
          display: flex;
          width: var(--rdp-day_button-width);
          height: var(--rdp-day_button-height);
          border: var(--rdp-day_button-border);
          border-radius: var(--rdp-day_button-border-radius);
          transition: all 0.2s ease;
        }

        .calendar-wrapper :global(.rdp-day_button:hover) {
          background-color: #f3f4f6;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .calendar-wrapper :global(.rdp-day_button:disabled) {
          cursor: revert;
        }

        .calendar-wrapper :global(.rdp-caption_label) {
          z-index: 1;
          position: relative;
          display: inline-flex;
          align-items: center;
          white-space: nowrap;
          border: 0;
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          text-transform: capitalize;
        }

        .calendar-wrapper :global(.rdp-dropdown:focus-visible ~ .rdp-caption_label) {
          outline: 5px auto Highlight;
          outline: 5px auto -webkit-focus-ring-color;
        }

        .calendar-wrapper :global(.rdp-button_next),
        .calendar-wrapper :global(.rdp-button_previous) {
          border: 1px solid #e5e7eb;
          background: white;
          padding: 0;
          margin: 0;
          cursor: pointer;
          font: inherit;
          color: #6b7280;
          -moz-appearance: none;
          -webkit-appearance: none;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          position: relative;
          appearance: none;
          width: var(--rdp-nav_button-width);
          height: var(--rdp-nav_button-height);
          border-radius: 8px;
          transition: all 0.2s ease;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .calendar-wrapper :global(.rdp-button_next:hover),
        .calendar-wrapper :global(.rdp-button_previous:hover) {
          background: #f9fafb;
          border-color: #d1d5db;
          color: #374151;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .calendar-wrapper :global(.rdp-button_next:disabled),
        .calendar-wrapper :global(.rdp-button_next[aria-disabled="true"]),
        .calendar-wrapper :global(.rdp-button_previous:disabled),
        .calendar-wrapper :global(.rdp-button_previous[aria-disabled="true"]) {
          cursor: revert;
          opacity: var(--rdp-nav_button-disabled-opacity);
        }

        .calendar-wrapper :global(.rdp-button_next:disabled:hover),
        .calendar-wrapper :global(.rdp-button_previous:disabled:hover) {
          transform: none;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .calendar-wrapper :global(.rdp-chevron) {
          display: inline-block;
          fill: currentColor;
          width: 16px;
          height: 16px;
        }

        .calendar-wrapper :global(.rdp-root[dir="rtl"] .rdp-nav .rdp-chevron) {
          transform: rotate(180deg);
          transform-origin: 50%;
        }

        .calendar-wrapper :global(.rdp-dropdowns) {
          position: relative;
          display: inline-flex;
          align-items: center;
          gap: var(--rdp-dropdown-gap);
        }

        .calendar-wrapper :global(.rdp-dropdown) {
          z-index: 2;
          opacity: 0;
          appearance: none;
          position: absolute;
          inset-block-start: 0;
          inset-block-end: 0;
          inset-inline-start: 0;
          width: 100%;
          margin: 0;
          padding: 0;
          cursor: inherit;
          border: none;
          line-height: inherit;
        }

        .calendar-wrapper :global(.rdp-dropdown_root) {
          position: relative;
          display: inline-flex;
          align-items: center;
        }

        .calendar-wrapper :global(.rdp-dropdown_root[data-disabled="true"] .rdp-chevron) {
          opacity: var(--rdp-disabled-opacity);
        }

        .calendar-wrapper :global(.rdp-month_caption) {
          display: flex;
          align-content: center;
          height: var(--rdp-nav-height);
          font-weight: bold;
          font-size: large;
          justify-content: space-between;
          align-items: center;
          padding: 0 8px 24px 8px;
        }

        .calendar-wrapper :global(.rdp-months) {
          position: relative;
          display: flex;
          flex-wrap: wrap;
          gap: var(--rdp-months-gap);
          max-width: fit-content;
        }

        .calendar-wrapper :global(.rdp-month_grid) {
          border-collapse: collapse;
          width: 100%;
        }

        .calendar-wrapper :global(.rdp-nav) {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .calendar-wrapper :global(.rdp-weekday) {
          opacity: var(--rdp-weekday-opacity);
          padding: var(--rdp-weekday-padding);
          font-weight: 600;
          font-size: 14px;
          text-align: var(--rdp-weekday-text-align);
          text-transform: uppercase;
          letter-spacing: 0.5px;
          color: #6b7280;
        }

        .calendar-wrapper :global(.rdp-week_number) {
          opacity: var(--rdp-week_number-opacity);
          font-weight: 400;
          font-size: small;
          height: var(--rdp-week_number-height);
          width: var(--rdp-week_number-width);
          border: var(--rdp-week_number-border);
          border-radius: var(--rdp-week_number-border-radius);
          text-align: var(--rdp-weeknumber-text-align);
        }

        .calendar-wrapper :global(.rdp-today:not(.rdp-outside)) {
          color: white;
          font-weight: 600;
        }

        .calendar-wrapper :global(.rdp-today .rdp-day_button) {
          background-color: var(--rdp-accent-color);
          color: white;
          box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
        }

        .calendar-wrapper :global(.rdp-today .rdp-day_button:hover) {
          background-color: #1d4ed8;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);
        }

        .calendar-wrapper :global(.rdp-selected) {
          font-weight: bold;
        }

        .calendar-wrapper :global(.rdp-selected .rdp-day_button) {
          border: var(--rdp-selected-border);
          background-color: var(--rdp-accent-color);
          color: white;
          box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
        }

        .calendar-wrapper :global(.rdp-selected .rdp-day_button:hover) {
          background-color: #1d4ed8;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);
        }

        .calendar-wrapper :global(.rdp-outside) {
          opacity: var(--rdp-outside-opacity);
        }

        .calendar-wrapper :global(.rdp-disabled) {
          opacity: var(--rdp-disabled-opacity);
        }

        .calendar-wrapper :global(.rdp-disabled .rdp-day_button) {
          cursor: not-allowed;
        }

        .calendar-wrapper :global(.rdp-disabled .rdp-day_button:hover) {
          background: transparent;
          transform: none;
          box-shadow: none;
        }

        .calendar-wrapper :global(.rdp-hidden) {
          visibility: hidden;
        }

        .calendar-wrapper :global(.rdp-focusable) {
          cursor: pointer;
        }


        .calendar-wrapper :global(.has-appointments::after) {
          content: '';
          position: absolute;
          bottom: 4px;
          left: 50%;
          transform: translateX(-50%);
          width: 6px;
          height: 6px;
          background-color: #3b82f6;
          border-radius: 50%;
          border: 1px solid white;
          z-index: 1;
        }

        .calendar-wrapper :global(.rdp-selected.has-appointments::after),
        .calendar-wrapper :global(.rdp-today.has-appointments::after) {
          background-color: white;
          border-color: var(--rdp-accent-color);
        }

        .calendar-wrapper :global(.has-availability .rdp-day_button) {
          background-color: #f0fdf4;
          border-color: #bbf7d0;
        }

        .calendar-wrapper :global(.has-availability .rdp-day_button:hover) {
          background-color: #dcfce7;
          border-color: #86efac;
        }


        @keyframes rdp-slide_in_left {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(0); }
        }

        @keyframes rdp-slide_in_right {
          0% { transform: translateX(100%); }
          100% { transform: translateX(0); }
        }

        @keyframes rdp-slide_out_left {
          0% { transform: translateX(0); }
          100% { transform: translateX(-100%); }
        }

        @keyframes rdp-slide_out_right {
          0% { transform: translateX(0); }
          100% { transform: translateX(100%); }
        }

        .calendar-wrapper :global(.rdp-weeks_before_enter) {
          animation: rdp-slide_in_left var(--rdp-animation_duration) var(--rdp-animation_timing) forwards;
        }

        .calendar-wrapper :global(.rdp-weeks_before_exit) {
          animation: rdp-slide_out_left var(--rdp-animation_duration) var(--rdp-animation_timing) forwards;
        }

        .calendar-wrapper :global(.rdp-weeks_after_enter) {
          animation: rdp-slide_in_right var(--rdp-animation_duration) var(--rdp-animation_timing) forwards;
        }

        .calendar-wrapper :global(.rdp-weeks_after_exit) {
          animation: rdp-slide_out_right var(--rdp-animation_duration) var(--rdp-animation_timing) forwards;
        }


        @media (max-width: 640px) {
          .calendar-wrapper {
            padding: 16px;
            max-width: 320px;
          }

          .calendar-wrapper :global(.rdp-root) {
            --rdp-day-height: 40px;
            --rdp-day-width: 40px;
            --rdp-day_button-height: 38px;
            --rdp-day_button-width: 38px;
            --rdp-nav_button-height: 2rem;
            --rdp-nav_button-width: 2rem;
            --rdp-weekday-padding: 0.5rem 0rem;
          }

          .calendar-wrapper :global(.rdp-caption_label) {
            font-size: 1rem;
          }

          .calendar-wrapper :global(.rdp-weekday) {
            font-size: 12px;
          }

          .calendar-wrapper :global(.rdp-month_caption) {
            padding: 0 4px 16px 4px;
          }
        }

        @media (max-width: 480px) {
          .calendar-wrapper {
            max-width: 280px;
          }

          .calendar-wrapper :global(.rdp-root) {
            --rdp-day-height: 36px;
            --rdp-day-width: 36px;
            --rdp-day_button-height: 34px;
            --rdp-day_button-width: 34px;
          }

          .calendar-wrapper :global(.rdp-day_button) {
            font-size: 13px;
          }

          .calendar-wrapper :global(.rdp-weekday) {
            font-size: 11px;
          }
        }
      `}</style>
      
      <DayPicker
        locale={ptBR}
        showOutsideDays={showOutsideDays}
        modifiers={modifiers}
        modifiersClassNames={modifiersClassNames}
        components={{
          IconLeft: ({ ...props }) => <ChevronLeft className="w-4 h-4" />,
          IconRight: ({ ...props }) => <ChevronRight className="w-4 h-4" />,
        }}
        navLayout="around"
        {...props}
      />
    </div>
  );
}

Calendar.displayName = "Calendar";

export { Calendar };
 */
