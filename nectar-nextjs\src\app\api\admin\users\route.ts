import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import { createAdminClient } from '@/lib/supabase/admin'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user is admin
      const { data: currentUser, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (userError) {
        return handleApiError(userError)
      }

      const isAdmin = currentUser?.role === 'admin'
      if (!isAdmin) {
        return createApiResponse(null, 'Forbidden', 403)
      }

      // Get all users from public.users table
      const { data: users, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(users || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user is admin
      const { data: currentUser, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (userError) {
        return handleApiError(userError)
      }

      const isAdmin = currentUser?.role === 'admin'
      if (!isAdmin) {
        return createApiResponse(null, 'Forbidden', 403)
      }

      const body = await request.json()
      const { email, name, phone, role, password, is_active = true } = body

      if (!email || !password || !name) {
        return createApiResponse(null, 'Email, password, and name are required', 400)
      }

      // Create user in auth.users using Supabase Admin API with service role
      const adminClient = createAdminClient()

      console.log('Creating user with data:', { email, role, name, phone })

      const { data: authUser, error: authError } = await adminClient.auth.admin.createUser({
        email,
        password,
        email_confirm: true, // Auto-confirm email
        user_metadata: {
          name,
          phone,
          role: role || 'user'
        }
      })

      if (authError) {
        console.error('Auth user creation error:', authError)
        return createApiResponse(null, `Failed to create user: ${authError.message}`, 400)
      }

      if (!authUser.user) {
        return createApiResponse(null, 'Failed to create user', 400)
      }

      // The triggers will automatically:
      // 1. Sync auth.users to public.users
      // 2. Auto-populate healthcare_professionals or secretaries table based on role
      // 3. Set role_id in users table for FK relationships

      console.log('✅ Auth user created successfully:', authUser.user.id)

      // Wait longer for triggers to complete
      console.log('⏳ Waiting for triggers to complete...')
      await new Promise(resolve => setTimeout(resolve, 500))

      // Fetch the created user from public.users
      console.log('🔍 Fetching user from public.users...')
      const { data: publicUser, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('id', authUser.user.id)
        .single()

      if (fetchError) {
        console.error('❌ Error fetching created user:', fetchError)

        // Try to create the user manually in public.users
        console.log('🔧 Attempting manual user creation in public.users...')
        const { data: manualUser, error: manualError } = await supabase
          .from('users')
          .insert({
            id: authUser.user.id,
            email,
            name,
            phone,
            role: role || 'healthcare_professional',
            is_active: true
          })
          .select()
          .single()

        if (manualError) {
          console.error('❌ Manual user creation failed:', manualError)
          // Return basic user info even if sync failed
          return createApiResponse({
            id: authUser.user.id,
            email: authUser.user.email,
            name,
            phone,
            role: role || 'user',
            is_active: true
          }, 'User created successfully (auth only)', 201)
        }

        console.log('✅ Manual user creation successful')
        return createApiResponse(manualUser, 'User created successfully', 201)
      }

      return createApiResponse(publicUser, 'User created successfully', 201)
    } catch (error) {
      console.error('User creation error:', error)
      return handleApiError(error)
    }
  })
}
