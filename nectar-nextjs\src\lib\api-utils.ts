import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { hasPermission, type Role } from '@/lib/permissions'

export interface ApiResponse<T = any> {
  data?: T
  error?: string
  message?: string
}

export function createApiResponse<T>(
  data?: T,
  message?: string,
  status: number = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json(
    {
      data,
      message,
    },
    { status }
  )
}

export async function withAuth<T>(
  request: NextRequest,
  handler: (userId: string, supabase: any) => Promise<NextResponse<ApiResponse<T>>>
): Promise<NextResponse<ApiResponse<T>>> {
  try {
    const supabase = await createClient()
    
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    console.log('🔐 Auth check:', {
      hasUser: !!user,
      userId: user?.id,
      email: user?.email,
      error: authError?.message
    })

    if (authError || !user) {
      console.error('❌ Auth failed:', authError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    return await handler(user.id, supabase)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function withAuthAndPermission<T>(
  request: NextRequest,
  resource: string,
  action: 'create' | 'read' | 'update' | 'delete',
  handler: (userId: string, supabase: any) => Promise<NextResponse<ApiResponse<T>>>
): Promise<NextResponse<ApiResponse<T>>> {
  try {
    const supabase = await createClient()

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return createApiResponse(undefined, 'Unauthorized', 401)
    }

    // Check permissions
    const hasAccess = await hasPermission(user.id, resource, action)
    if (!hasAccess) {
      return createApiResponse(undefined, 'Forbidden: Insufficient permissions', 403)
    }

    return await handler(user.id, supabase)
  } catch (error) {
    console.error('API Error:', error)
    return createApiResponse(
      undefined,
      error instanceof Error ? error.message : 'Internal server error',
      500
    )
  }
}

export function handleApiError(error: any): NextResponse<ApiResponse> {
  console.error('API Error:', error)

  if (error?.code === 'PGRST116') {
    return NextResponse.json({ error: 'Resource not found' }, { status: 404 })
  }

  if (error?.code === '23505') {
    return NextResponse.json({ error: 'Resource already exists' }, { status: 409 })
  }

  return NextResponse.json(
    { error: error?.message || 'Internal server error' },
    { status: 500 }
  )
}
