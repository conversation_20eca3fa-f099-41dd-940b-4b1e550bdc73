# Nectar - Sistema de Gestão de Clínicas

Sistema completo de gestão para clínicas médicas desenvolvido com Next.js, TypeScript e Supabase.

## 🚀 Funcionalidades

### 📅 Agenda Avançada
- **Calendário Duplo**: Visualização em calendário tradicional e agenda completa com FullCalendar
- **Agendamento Inteligente**: Blocos de 30 minutos com drag-and-drop
- **Recorrência Personalizada**: Consultas recorrentes com configurações flexíveis
- **Filtros por Profissional**: Visualização específica por médico/profissional
- **Indicadores Visuais**: Contadores de consultas por dia no calendário

### 👥 Gestão de Pacientes
- **Cadastro Completo**: Dados pessoais, contato e observações
- **Histórico de Consultas**: Visualização completa com status e valores
- **Sistema de Anexos**: Upload e gerenciamento de documentos
- **Validação Avançada**: CPF, telefone e e-mail com formatação automática

### 🏥 Profissionais de Saúde
- **Cadastro de Médicos**: Especialidades, CRM e dados de contato
- **Gestão de Disponibilidade**: Horários e dias de atendimento
- **Controle de Status**: Ativo/inativo para organização

### 💼 Procedimentos e Preços
- **Catálogo de Procedimentos**: Nome, descrição, preço e duração
- **Associação a Consultas**: Múltiplos procedimentos por consulta
- **Cálculo Automático**: Preços e durações baseados nos procedimentos

### 🔐 Controle de Acesso
- **Roles Hierárquicos**: Admin, Médico, Secretária, Assistente
- **Permissões Granulares**: Controle por recurso e ação
- **Middleware de Segurança**: Proteção automática das APIs

### ⚙️ Configurações da Clínica
- **Horários de Funcionamento**: Configuração flexível por dia
- **Duração de Consultas**: Personalização dos blocos de tempo
- **Fuso Horário**: Suporte a diferentes regiões
- **Fins de Semana**: Opção para agendamentos

## 🛠️ Tecnologias

### Frontend
- **Next.js 14**: Framework React com App Router
- **TypeScript**: Tipagem estática para maior segurança
- **Tailwind CSS**: Estilização utilitária e responsiva
- **Shadcn/ui**: Componentes de interface modernos
- **FullCalendar**: Calendário interativo e profissional
- **React Hook Form**: Gerenciamento de formulários
- **Zod**: Validação de esquemas

### Backend
- **Supabase**: Backend-as-a-Service completo
- **PostgreSQL**: Banco de dados relacional
- **Row Level Security**: Segurança nativa do Supabase
- **Storage**: Armazenamento de arquivos
- **Auth**: Autenticação integrada

### Qualidade
- **Jest**: Framework de testes
- **Testing Library**: Testes de componentes React
- **ESLint**: Linting de código
- **Prettier**: Formatação automática

## 📱 Design Responsivo

- **Mobile First**: Otimizado para dispositivos móveis
- **Breakpoints Customizados**: xs, sm, md, lg, xl, 2xl
- **Touch Interactions**: Gestos otimizados para mobile
- **Adaptive UI**: Interface que se adapta ao tamanho da tela

## 🚀 Instalação

### Pré-requisitos
- Node.js 18+
- npm ou yarn
- Conta no Supabase

### Configuração

1. **Clone o repositório**
```bash
git clone <repository-url>
cd nectar-nextjs
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure as variáveis de ambiente**
```bash
cp .env.example .env.local
```

Edite `.env.local` com suas credenciais do Supabase:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

4. **Inicie o servidor de desenvolvimento**
```bash
npm run dev
```

## 🧪 Testes

### Executar todos os testes
```bash
npm test
```

### Executar testes em modo watch
```bash
npm run test:watch
```

### Gerar relatório de cobertura
```bash
npm run test:coverage
```

## 📚 Documentação da API

### Endpoints Principais

#### Consultas
- `GET /api/appointments` - Listar consultas
- `POST /api/appointments` - Criar consulta
- `PUT /api/appointments/[id]` - Atualizar consulta
- `DELETE /api/appointments/[id]` - Excluir consulta

#### Pacientes
- `GET /api/patients` - Listar pacientes
- `POST /api/patients` - Criar paciente
- `PUT /api/patients/[id]` - Atualizar paciente

#### Profissionais
- `GET /api/healthcare-professionals` - Listar profissionais
- `POST /api/healthcare-professionals` - Criar profissional

### Autenticação

Todas as APIs requerem autenticação via JWT token do Supabase.

```javascript
// Exemplo de requisição
const response = await fetch('/api/appointments', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
```

## 🔒 Segurança

### Row Level Security (RLS)
- Todos os dados são isolados por usuário
- Políticas automáticas no Supabase
- Prevenção de vazamento de dados

### Validação
- Esquemas Zod em todas as entradas
- Sanitização automática
- Validação client-side e server-side

### Permissões
- Sistema baseado em roles
- Middleware de verificação
- Controle granular por recurso

## 🌐 Internacionalização

- **Idioma**: Português (pt-BR)
- **Formato de Data**: DD/MM/YYYY
- **Moeda**: Real (R$)
- **Fuso Horário**: America/Sao_Paulo

---

Desenvolvido com ❤️ para modernizar a gestão de clínicas médicas.
