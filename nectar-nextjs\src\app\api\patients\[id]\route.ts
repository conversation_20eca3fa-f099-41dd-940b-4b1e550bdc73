import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesUpdate } from '@/types/supabase'

type Patient = Tables<'patients'>
type PatientUpdate = TablesUpdate<'patients'>

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params

  return withAuth(request, async (userId, supabase) => {
    try {
      // Use the new get_accessible_patients function to check access
      const { data: accessiblePatients, error } = await supabase
        .rpc('get_accessible_patients', { current_user_id: userId })

      if (error) {
        return handleApiError(error)
      }

      // Find the specific patient in the accessible patients list
      const patient = accessiblePatients?.find((p: any) => p.id === id)

      if (!patient) {
        return createApiResponse(null, 'Patient not found or access denied', 404)
      }

      return createApiResponse(patient)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params

  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user has access to this patient
      const { data: accessiblePatients, error: accessError } = await supabase
        .rpc('get_accessible_patients', { current_user_id: userId })

      if (accessError) {
        return handleApiError(accessError)
      }

      const hasAccess = accessiblePatients?.some((p: any) => p.id === id)
      if (!hasAccess) {
        return createApiResponse(null, 'Patient not found or access denied', 404)
      }

      const body = await request.json()

      const updateData: PatientUpdate = {
        ...body,
        birth_date: body.birth_date || null
      }

      const { data: patient, error } = await supabase
        .from('patients')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(patient)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params

  return withAuth(request, async (userId, supabase) => {
    try {
      console.log('🔄 PATCH Patient ID:', id, 'by user:', userId);

      // Check if user has access to this patient
      const { data: accessiblePatients, error: accessError } = await supabase
        .rpc('get_accessible_patients', { current_user_id: userId })

      if (accessError) {
        console.error('❌ Access check error:', accessError);
        return handleApiError(accessError)
      }

      const hasAccess = accessiblePatients?.some((p: any) => p.id === id)
      if (!hasAccess) {
        console.error('❌ Access denied for patient:', id, 'user:', userId);
        return createApiResponse(null, 'Patient not found or access denied', 404)
      }

      const body = await request.json()
      console.log('📝 Update data:', body);

      const updateData: PatientUpdate = {
        name: body.name,
        email: body.email || null,
        phone: body.phone || null,
        birth_date: body.birth_date || null,
        cpf: body.cpf || null,
        address: body.address || null,
        notes: body.notes || null
      }

      const { data: patient, error } = await supabase
        .from('patients')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('❌ Database update error:', error);
        return handleApiError(error)
      }

      // Handle healthcare professional associations if provided
      if (body.healthcare_professional_ids && Array.isArray(body.healthcare_professional_ids)) {
        console.log('🔗 Managing healthcare professional associations:', body.healthcare_professional_ids);
        
        // Remove all existing associations for this patient
        await supabase
          .from('patient_healthcare_professional_associations')
          .delete()
          .eq('patient_id', id)

        // Add new associations
        if (body.healthcare_professional_ids.length > 0) {
          const associations = body.healthcare_professional_ids.map((professionalId: string) => ({
            patient_id: id,
            healthcare_professional_id: professionalId,
            created_by: userId
          }))

          const { error: associationError } = await supabase
            .from('patient_healthcare_professional_associations')
            .insert(associations)

          if (associationError) {
            console.error('❌ Association error:', associationError);
            // Don't fail the update, just log the error
          } else {
            console.log('✅ Healthcare professional associations updated');
          }
        }
      } else if (body.healthcare_professional_id) {
        // Support for legacy single professional ID (backwards compatibility)
        console.log('🔗 Managing single healthcare professional association:', body.healthcare_professional_id);
        
        // Remove all existing associations for this patient
        await supabase
          .from('patient_healthcare_professional_associations')
          .delete()
          .eq('patient_id', id)

        // Add new association
        const { error: associationError } = await supabase
          .from('patient_healthcare_professional_associations')
          .insert({
            patient_id: id,
            healthcare_professional_id: body.healthcare_professional_id,
            created_by: userId
          })

        if (associationError) {
          console.error('❌ Association error:', associationError);
          // Don't fail the update, just log the error
        } else {
          console.log('✅ Healthcare professional association updated');
        }
      } else {
        // If no healthcare_professional_ids provided, remove existing associations
        console.log('🗑️ Removing existing associations');
        await supabase
          .from('patient_healthcare_professional_associations')
          .delete()
          .eq('patient_id', id)
      }

      console.log('✅ Patient updated successfully:', patient.id);
      return createApiResponse(patient)
    } catch (error) {
      console.error('❌ PATCH error:', error);
      return handleApiError(error)
    }
  })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      console.log('🗑️ DELETE Patient ID:', id, 'by user:', userId);

      // Check if user has access to this patient
      const { data: accessiblePatients, error: accessError } = await supabase
        .rpc('get_accessible_patients', { current_user_id: userId })

      if (accessError) {
        console.error('❌ Access check error:', accessError);
        return handleApiError(accessError)
      }

      const hasAccess = accessiblePatients?.some((p: any) => p.id === id)
      if (!hasAccess) {
        console.error('❌ Access denied for patient:', id, 'user:', userId);
        return createApiResponse(null, 'Patient not found or access denied', 404)
      }

      const { error } = await supabase
        .from('patients')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('❌ Database delete error:', error);
        return handleApiError(error)
      }

      console.log('✅ Patient deleted successfully:', id);
      return createApiResponse({ success: true })
    } catch (error) {
      console.error('❌ DELETE error:', error);
      return handleApiError(error)
    }
  })
}
