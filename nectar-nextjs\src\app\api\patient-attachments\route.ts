import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type PatientAttachment = Tables<'patient_attachments'>
type PatientAttachmentInsert = TablesInsert<'patient_attachments'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const patientId = searchParams.get('patient_id')

      let query = supabase
        .from('patient_attachments')
        .select('*')
        .eq('user_id', userId)

      if (patientId) {
        query = query.eq('patient_id', patientId)
      }

      const { data: attachments, error } = await query.order('created_at', { ascending: false })

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(attachments || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const formData = await request.formData()
      const file = formData.get('file') as File
      const patientId = formData.get('patient_id') as string

      if (!file || !patientId) {
        return createApiResponse(null, 'File and patient_id are required', 400)
      }

      // Upload file to Supabase Storage
      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}.${fileExt}`
      const filePath = `patient-attachments/${userId}/${patientId}/${fileName}`

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('patient-files')
        .upload(filePath, file)

      if (uploadError) {
        return handleApiError(uploadError)
      }

      // Save attachment metadata to database
      const attachmentData: PatientAttachmentInsert = {
        user_id: userId,
        patient_id: patientId,
        file_name: file.name,
        file_path: uploadData.path,
        file_size: file.size,
        mime_type: file.type
      }

      const { data: attachment, error } = await supabase
        .from('patient_attachments')
        .insert(attachmentData)
        .select()
        .single()

      if (error) {
        // Clean up uploaded file if database insert fails
        await supabase.storage
          .from('patient-files')
          .remove([uploadData.path])
        return handleApiError(error)
      }

      return createApiResponse(attachment, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
