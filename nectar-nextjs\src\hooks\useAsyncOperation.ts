import { useState, useCallback } from 'react'
import { useToast } from '@/hooks/use-toast'

interface UseAsyncOperationOptions {
  onSuccess?: (data?: any) => void
  onError?: (error: Error) => void
  successMessage?: string
  errorMessage?: string
  showSuccessToast?: boolean
  showErrorToast?: boolean
}

interface UseAsyncOperationReturn<T> {
  data: T | null
  loading: boolean
  error: Error | null
  execute: (operation: () => Promise<T>) => Promise<T | null>
  reset: () => void
}

export function useAsyncOperation<T = any>(
  options: UseAsyncOperationOptions = {}
): UseAsyncOperationReturn<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { toast } = useToast()

  const {
    onSuccess,
    onError,
    successMessage,
    errorMessage,
    showSuccessToast = true,
    showErrorToast = true
  } = options

  const execute = useCallback(async (operation: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await operation()
      setData(result)
      
      if (showSuccessToast && successMessage) {
        toast({
          title: "Sucesso!",
          description: successMessage,
        })
      }
      
      onSuccess?.(result)
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Erro desconhecido')
      setError(error)
      
      if (showErrorToast) {
        toast({
          title: "Erro",
          description: errorMessage || error.message || 'Ocorreu um erro inesperado',
          variant: "destructive"
        })
      }
      
      onError?.(error)
      return null
    } finally {
      setLoading(false)
    }
  }, [onSuccess, onError, successMessage, errorMessage, showSuccessToast, showErrorToast, toast])

  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setLoading(false)
  }, [])

  return {
    data,
    loading,
    error,
    execute,
    reset
  }
}

// Specialized hook for API operations
export function useApiOperation<T = any>(options: UseAsyncOperationOptions = {}) {
  return useAsyncOperation<T>({
    showSuccessToast: true,
    showErrorToast: true,
    ...options
  })
}

// Hook for form submissions
export function useFormSubmission<T = any>(options: UseAsyncOperationOptions = {}) {
  return useAsyncOperation<T>({
    showSuccessToast: true,
    showErrorToast: true,
    successMessage: 'Dados salvos com sucesso!',
    errorMessage: 'Erro ao salvar dados',
    ...options
  })
}

// Hook for data fetching
export function useDataFetching<T = any>(options: UseAsyncOperationOptions = {}) {
  return useAsyncOperation<T>({
    showSuccessToast: false,
    showErrorToast: true,
    errorMessage: 'Erro ao carregar dados',
    ...options
  })
}

// Hook for delete operations
export function useDeleteOperation(options: UseAsyncOperationOptions = {}) {
  return useAsyncOperation({
    showSuccessToast: true,
    showErrorToast: true,
    successMessage: 'Item excluído com sucesso!',
    errorMessage: 'Erro ao excluir item',
    ...options
  })
}
