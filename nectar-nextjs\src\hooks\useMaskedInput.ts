import { useState, useCallback } from 'react';

// Função para aplicar máscara de telefone (11) 99999-9999
export const applyPhoneMask = (value: string): string => {
  if (!value) return '';
  
  // Remove todos os caracteres não numéricos
  const numbers = value.replace(/\D/g, '');
  
  // Aplica a máscara baseada no tamanho
  if (numbers.length <= 2) {
    return `(${numbers}`;
  } else if (numbers.length <= 7) {
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
  } else if (numbers.length <= 11) {
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
  } else {
    // Limita a 11 dígitos
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
  }
};

// Função para aplicar máscara de CPF 999.999.999-99
export const applyCpfMask = (value: string): string => {
  if (!value) return '';
  
  // Remove todos os caracteres não numéricos
  const numbers = value.replace(/\D/g, '');
  
  // Aplica a máscara baseada no tamanho
  if (numbers.length <= 3) {
    return numbers;
  } else if (numbers.length <= 6) {
    return `${numbers.slice(0, 3)}.${numbers.slice(3)}`;
  } else if (numbers.length <= 9) {
    return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6)}`;
  } else if (numbers.length <= 11) {
    return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6, 9)}-${numbers.slice(9)}`;
  } else {
    // Limita a 11 dígitos
    return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6, 9)}-${numbers.slice(9, 11)}`;
  }
};

// Função para remover máscara e manter apenas números
export const removePhoneMask = (value: string): string => {
  return value.replace(/\D/g, '');
};

export const removeCpfMask = (value: string): string => {
  return value.replace(/\D/g, '');
};

// Hook personalizado para gerenciar campos com máscara
export const useMaskedInput = (initialValue: string = '', maskType: 'phone' | 'cpf') => {
  const [value, setValue] = useState(initialValue);
  const [rawValue, setRawValue] = useState(
    maskType === 'phone' ? removePhoneMask(initialValue) : removeCpfMask(initialValue)
  );

  const handleChange = useCallback((newValue: string) => {
    const applyMask = maskType === 'phone' ? applyPhoneMask : applyCpfMask;
    const removeMask = maskType === 'phone' ? removePhoneMask : removeCpfMask;
    
    const masked = applyMask(newValue);
    const raw = removeMask(newValue);
    
    setValue(masked);
    setRawValue(raw);
  }, [maskType]);

  return {
    value,
    rawValue,
    onChange: handleChange,
    setValue,
    setRawValue
  };
};

// Validação de CPF
export const validateCpf = (cpf: string): boolean => {
  const numbers = cpf.replace(/\D/g, '');
  
  if (numbers.length !== 11) return false;
  
  // Verifica se todos os dígitos são iguais
  if (numbers.split('').every(digit => digit === numbers[0])) return false;
  
  // Validação do primeiro dígito verificador
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(numbers[i]) * (10 - i);
  }
  let digit1 = 11 - (sum % 11);
  if (digit1 > 9) digit1 = 0;
  
  if (parseInt(numbers[9]) !== digit1) return false;
  
  // Validação do segundo dígito verificador
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(numbers[i]) * (11 - i);
  }
  let digit2 = 11 - (sum % 11);
  if (digit2 > 9) digit2 = 0;
  
  return parseInt(numbers[10]) === digit2;
};

// Validação de telefone (básica)
export const validatePhone = (phone: string): boolean => {
  const numbers = phone.replace(/\D/g, '');
  return numbers.length >= 10 && numbers.length <= 11;
};
