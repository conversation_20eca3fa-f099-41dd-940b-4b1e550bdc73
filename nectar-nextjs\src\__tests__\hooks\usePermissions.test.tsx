import { renderHook, waitFor } from '@testing-library/react'
import { usePermissions, PermissionGate, RoleGate } from '@/hooks/usePermissions'
import { makeAuthenticatedRequest } from '@/lib/api-client'
import { render, screen } from '@testing-library/react'

// Mock the API client
jest.mock('@/lib/api-client')
const mockMakeAuthenticatedRequest = makeAuthenticatedRequest as jest.MockedFunction<typeof makeAuthenticatedRequest>

describe('usePermissions', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should fetch and return user permissions and roles', async () => {
    const mockRoles = [
      { role_name: 'doctor' },
      { role_name: 'admin' }
    ]
    
    const mockPermissions = [
      { resource: 'appointments', action: 'read' },
      { resource: 'appointments', action: 'create' },
      { resource: 'patients', action: 'read' }
    ]

    mockMakeAuthenticatedRequest
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockRoles })
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockPermissions })
      } as Response)

    const { result } = renderHook(() => usePermissions())

    expect(result.current.loading).toBe(true)

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.roles).toEqual(['doctor', 'admin'])
    expect(result.current.permissions).toEqual(mockPermissions)
    expect(result.current.isAdmin).toBe(true)
    expect(result.current.hasRole('doctor')).toBe(true)
    expect(result.current.hasRole('secretary')).toBe(false)
    expect(result.current.hasPermission('appointments', 'read')).toBe(true)
    expect(result.current.hasPermission('appointments', 'delete')).toBe(false)
  })

  it('should handle API errors gracefully', async () => {
    mockMakeAuthenticatedRequest
      .mockRejectedValueOnce(new Error('Network error'))
      .mockRejectedValueOnce(new Error('Network error'))

    const { result } = renderHook(() => usePermissions())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.roles).toEqual([])
    expect(result.current.permissions).toEqual([])
    expect(result.current.isAdmin).toBe(false)
  })
})

describe('PermissionGate', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render children when user has permission', async () => {
    const mockPermissions = [
      { resource: 'appointments', action: 'create' }
    ]

    mockMakeAuthenticatedRequest
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: [] })
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockPermissions })
      } as Response)

    render(
      <PermissionGate resource="appointments" action="create">
        <div>Create Appointment Button</div>
      </PermissionGate>
    )

    await waitFor(() => {
      expect(screen.getByText('Create Appointment Button')).toBeInTheDocument()
    })
  })

  it('should render fallback when user lacks permission', async () => {
    const mockPermissions = [
      { resource: 'appointments', action: 'read' }
    ]

    mockMakeAuthenticatedRequest
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: [] })
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockPermissions })
      } as Response)

    render(
      <PermissionGate 
        resource="appointments" 
        action="delete"
        fallback={<div>Access Denied</div>}
      >
        <div>Delete Button</div>
      </PermissionGate>
    )

    await waitFor(() => {
      expect(screen.getByText('Access Denied')).toBeInTheDocument()
      expect(screen.queryByText('Delete Button')).not.toBeInTheDocument()
    })
  })
})

describe('RoleGate', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render children when user has required role', async () => {
    const mockRoles = [
      { role_name: 'admin' }
    ]

    mockMakeAuthenticatedRequest
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockRoles })
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: [] })
      } as Response)

    render(
      <RoleGate roles={['admin']}>
        <div>Admin Panel</div>
      </RoleGate>
    )

    await waitFor(() => {
      expect(screen.getByText('Admin Panel')).toBeInTheDocument()
    })
  })

  it('should render fallback when user lacks required role', async () => {
    const mockRoles = [
      { role_name: 'secretary' }
    ]

    mockMakeAuthenticatedRequest
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockRoles })
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: [] })
      } as Response)

    render(
      <RoleGate 
        roles={['admin']}
        fallback={<div>Insufficient Privileges</div>}
      >
        <div>Admin Panel</div>
      </RoleGate>
    )

    await waitFor(() => {
      expect(screen.getByText('Insufficient Privileges')).toBeInTheDocument()
      expect(screen.queryByText('Admin Panel')).not.toBeInTheDocument()
    })
  })

  it('should handle requireAll=true correctly', async () => {
    const mockRoles = [
      { role_name: 'doctor' }
    ]

    mockMakeAuthenticatedRequest
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockRoles })
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: [] })
      } as Response)

    render(
      <RoleGate 
        roles={['doctor', 'admin']}
        requireAll={true}
        fallback={<div>Need Both Roles</div>}
      >
        <div>Special Content</div>
      </RoleGate>
    )

    await waitFor(() => {
      expect(screen.getByText('Need Both Roles')).toBeInTheDocument()
      expect(screen.queryByText('Special Content')).not.toBeInTheDocument()
    })
  })
})
