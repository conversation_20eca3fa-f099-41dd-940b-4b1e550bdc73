"use client"

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Users, Plus, Search, Edit, Trash2, Phone, Mail, Calendar, Stethoscope } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { makeAuthenticatedRequest } from '@/lib/api-client';
import { PatientTableSkeleton, LoadingContainer } from '@/components/ui/skeleton-components';
import { MultiProfessionalSearch } from '@/components/ui/multi-professional-search';

type Patient = {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  birth_date?: string;
  cpf?: string;
  address?: string;
  notes?: string;
  created_at: string;
};

type HealthcareProfessional = {
  id: string;
  name: string;
  specialty?: string;
  phone?: string;
  is_active: boolean;
};

const PatientsPage = () => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [healthcareProfessionals, setHealthcareProfessionals] = useState<HealthcareProfessional[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingPatient, setEditingPatient] = useState<Patient | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();
  const router = useRouter();

  const [patientForm, setPatientForm] = useState({
    name: '',
    email: '',
    phone: '',
    birth_date: '',
    cpf: '',
    address: '',
    notes: '',
    healthcare_professional_ids: [] as string[]  // Mudança para array
  });

  useEffect(() => {
    fetchPatients();
    fetchHealthcareProfessionals();
  }, []);

  useEffect(() => {
    const filtered = patients.filter(patient =>
      patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.phone?.includes(searchTerm)
    );
    setFilteredPatients(filtered);
  }, [patients, searchTerm]);

  const fetchPatients = async () => {
    try {
      setLoading(true);
      const response = await makeAuthenticatedRequest('/api/patients');

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', response.status, errorText);
        throw new Error(`Failed to fetch patients: ${response.status}`);
      }

      const result = await response.json();
      console.log('Patients API response:', result);
      const data = result.data || result;
      setPatients(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Fetch patients error:', error);
      setPatients([]);
      toast({
        title: "Erro ao carregar pacientes",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchHealthcareProfessionals = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/healthcare-professionals');
      if (!response.ok) throw new Error('Failed to fetch healthcare professionals');
      const result = await response.json();
      const data = result.data || result;
      setHealthcareProfessionals(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching healthcare professionals:', error);
      setHealthcareProfessionals([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const method = editingPatient ? 'PATCH' : 'POST';
      const url = editingPatient ? `/api/patients/${editingPatient.id}` : '/api/patients';
      
      console.log('🔄 Submitting patient form:', { method, url, data: patientForm });
      
      const response = await makeAuthenticatedRequest(url, {
        method,
        body: JSON.stringify(patientForm)
      });

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error:', response.status, errorText);
        throw new Error(`Failed to save patient: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Success result:', result);

      toast({
        title: editingPatient ? "Paciente atualizado" : "Paciente criado",
        description: editingPatient ? "Os dados foram atualizados com sucesso" : "Novo paciente adicionado com sucesso"
      });

      setDialogOpen(false);
      setEditingPatient(null);
      setPatientForm({
        name: '',
        email: '',
        phone: '',
        birth_date: '',
        cpf: '',
        address: '',
        notes: '',
        healthcare_professional_ids: []
      });
      
      fetchPatients();
    } catch (error) {
      toast({
        title: "Erro ao salvar paciente",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    }
  };

  const handleEdit = async (patient: Patient) => {
    setEditingPatient(patient);
    
    // Fetch current healthcare professional associations
    let currentHealthcareProfessionalIds: string[] = [];
    try {
      const response = await makeAuthenticatedRequest(`/api/patients/${patient.id}/associations`);
      if (response.ok) {
        const result = await response.json();
        const associations = result.data || [];
        currentHealthcareProfessionalIds = associations.map((assoc: any) => assoc.healthcare_professional_id);
      }
    } catch (error) {
      console.error('Error fetching patient associations:', error);
    }

    setPatientForm({
      name: patient.name,
      email: patient.email || '',
      phone: patient.phone || '',
      birth_date: patient.birth_date || '',
      cpf: patient.cpf || '',
      address: patient.address || '',
      notes: patient.notes || '',
      healthcare_professional_ids: currentHealthcareProfessionalIds
    });
    setDialogOpen(true);
  };

  const handleDelete = async (patientId: string) => {
    if (!confirm('Tem certeza que deseja excluir este paciente?')) return;

    try {
      console.log('🗑️ Deleting patient:', patientId);
      
      const response = await makeAuthenticatedRequest(`/api/patients/${patientId}`, {
        method: 'DELETE'
      });

      console.log('📡 Delete response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Delete API Error:', response.status, errorText);
        throw new Error(`Failed to delete patient: ${response.status} - ${errorText}`);
      }

      console.log('✅ Patient deleted successfully');

      toast({
        title: "Paciente excluído",
        description: "O paciente foi removido com sucesso"
      });
      
      fetchPatients();
    } catch (error) {
      toast({
        title: "Erro ao excluir paciente",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    }
  };

  const openNewPatientDialog = () => {
    setEditingPatient(null);
    setPatientForm({
      name: '',
      email: '',
      phone: '',
      birth_date: '',
      cpf: '',
      address: '',
      notes: '',
      healthcare_professional_ids: []
    });
    setDialogOpen(true);
  };

  const handlePatientClick = (patientId: string) => {
    router.push(`/dashboard/pacientes/${patientId}`);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 w-[200px] bg-muted animate-pulse rounded" />
            <div className="h-4 w-[300px] bg-muted animate-pulse rounded" />
          </div>
          <div className="h-10 w-[150px] bg-muted animate-pulse rounded" />
        </div>

        <div className="space-y-4">
          <div className="h-10 w-[300px] bg-muted animate-pulse rounded" />
          <PatientTableSkeleton rows={8} />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Pacientes</h1>
          <p className="text-muted-foreground">Gerencie seus pacientes e informações</p>
        </div>
        
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openNewPatientDialog}>
              <Plus className="mr-2 h-4 w-4" />
              Novo Paciente
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>
                  {editingPatient ? 'Editar Paciente' : 'Novo Paciente'}
                </DialogTitle>
                <DialogDescription>
                  {editingPatient ? 'Atualize as informações do paciente' : 'Adicione um novo paciente ao sistema'}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome *</Label>
                  <Input
                    id="name"
                    value={patientForm.name}
                    onChange={(e) => setPatientForm({ ...patientForm, name: e.target.value })}
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={patientForm.email}
                      onChange={(e) => setPatientForm({ ...patientForm, email: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Telefone</Label>
                    <Input
                      id="phone"
                      value={patientForm.phone}
                      onChange={(e) => setPatientForm({ ...patientForm, phone: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="birth_date">Data de Nascimento</Label>
                    <Input
                      id="birth_date"
                      type="date"
                      value={patientForm.birth_date}
                      onChange={(e) => setPatientForm({ ...patientForm, birth_date: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cpf">CPF</Label>
                    <Input
                      id="cpf"
                      value={patientForm.cpf}
                      onChange={(e) => setPatientForm({ ...patientForm, cpf: e.target.value })}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Endereço</Label>
                  <Input
                    id="address"
                    value={patientForm.address}
                    onChange={(e) => setPatientForm({ ...patientForm, address: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">Observações</Label>
                  <Textarea
                    id="notes"
                    value={patientForm.notes}
                    onChange={(e) => setPatientForm({ ...patientForm, notes: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="healthcare_professional">
                    <Stethoscope className="inline mr-1 h-4 w-4" />
                    Profissionais de Saúde (Opcional)
                  </Label>
                  <MultiProfessionalSearch
                    professionals={healthcareProfessionals.filter(prof => prof.is_active)}
                    selectedProfessionalIds={patientForm.healthcare_professional_ids}
                    onSelectionChange={(ids) => setPatientForm({ ...patientForm, healthcare_professional_ids: ids })}
                    placeholder="Buscar profissionais por nome ou especialidade..."
                  />
                  <p className="text-sm text-muted-foreground">
                    Selecione um ou mais profissionais para associar este paciente. Isso permitirá que os profissionais vejam este paciente em suas listas.
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">
                  {editingPatient ? 'Atualizar' : 'Criar'} Paciente
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5 text-primary" />
            Lista de Pacientes
          </CardTitle>
          <CardDescription>
            {filteredPatients.length} paciente(s) encontrado(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar pacientes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {filteredPatients.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p>Nenhum paciente encontrado</p>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden md:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Contato</TableHead>
                      <TableHead>Data de Nascimento</TableHead>
                      <TableHead>Cadastrado em</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPatients.map((patient) => (
                      <TableRow
                        key={patient.id}
                        className="cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => handlePatientClick(patient.id)}
                      >
                        <TableCell>
                          <div>
                            <p className="font-medium">{patient.name}</p>
                            {patient.cpf && (
                              <p className="text-sm text-muted-foreground">CPF: {patient.cpf}</p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {patient.email && (
                              <div className="flex items-center text-sm">
                                <Mail className="mr-1 h-3 w-3" />
                                {patient.email}
                              </div>
                            )}
                            {patient.phone && (
                              <div className="flex items-center text-sm">
                                <Phone className="mr-1 h-3 w-3" />
                                {patient.phone}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {patient.birth_date ? (
                            <div className="flex items-center text-sm">
                              <Calendar className="mr-1 h-3 w-3" />
                              {format(new Date(patient.birth_date), 'dd/MM/yyyy')}
                            </div>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {format(new Date(patient.created_at), 'dd/MM/yyyy')}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(patient);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(patient.id);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Card View */}
              <div className="md:hidden space-y-4">
                {filteredPatients.map((patient) => (
                  <Card
                    key={patient.id}
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => handlePatientClick(patient.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                          <h3 className="font-medium text-lg">{patient.name}</h3>
                          {patient.cpf && (
                            <p className="text-sm text-muted-foreground">CPF: {patient.cpf}</p>
                          )}
                        </div>
                        <div className="flex space-x-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(patient);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(patient.id);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        {patient.email && (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Mail className="mr-2 h-4 w-4 flex-shrink-0" />
                            <span className="truncate">{patient.email}</span>
                          </div>
                        )}
                        {patient.phone && (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Phone className="mr-2 h-4 w-4 flex-shrink-0" />
                            <span>{patient.phone}</span>
                          </div>
                        )}
                        {patient.birth_date && (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Calendar className="mr-2 h-4 w-4 flex-shrink-0" />
                            <span>Nascimento: {format(new Date(patient.birth_date), 'dd/MM/yyyy')}</span>
                          </div>
                        )}
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="mr-2 h-4 w-4 flex-shrink-0" />
                          <span>Cadastrado: {format(new Date(patient.created_at), 'dd/MM/yyyy')}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PatientsPage;
