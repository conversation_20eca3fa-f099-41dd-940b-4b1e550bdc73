import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const patientId = searchParams.get('patient_id')
      const healthcareProfessionalId = searchParams.get('healthcare_professional_id')

      let query = supabase
        .from('patient_healthcare_professional_associations')
        .select(`
          id,
          patient_id,
          healthcare_professional_id,
          created_by,
          created_at,
          updated_at,
          patients:patient_id (
            id,
            name,
            email,
            phone
          ),
          healthcare_professionals:healthcare_professional_id (
            id,
            name,
            specialty,
            user_id
          ),
          created_by_user:created_by (
            id,
            name,
            email
          )
        `)

      if (patientId) {
        query = query.eq('patient_id', patientId)
      }

      if (healthcareProfessionalId) {
        query = query.eq('healthcare_professional_id', healthcareProfessionalId)
      }

      const { data: associations, error } = await query

      if (error) {
        console.error('Error fetching patient-healthcare professional associations:', error)
        return handleApiError(error)
      }

      return createApiResponse(associations || [])
    } catch (error) {
      console.error('Unexpected error in patient-healthcare professional associations API:', error)
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const associationData = {
        patient_id: body.patient_id,
        healthcare_professional_id: body.healthcare_professional_id,
        created_by: userId
      }

      const { data: association, error } = await supabase
        .from('patient_healthcare_professional_associations')
        .insert(associationData)
        .select()
        .single()

      if (error) {
        console.error('Error creating patient-healthcare professional association:', error)
        return handleApiError(error)
      }

      return createApiResponse(association, undefined, 201)
    } catch (error) {
      console.error('Unexpected error creating patient-healthcare professional association:', error)
      return handleApiError(error)
    }
  })
}
