"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { EventInput, DateSelectArg, EventClickArg, EventDropArg } from '@fullcalendar/core';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Filter, Plus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { getCalendarStatusColor, type AppointmentStatus } from '@/lib/status-colors';

type AppointmentBlock = {
  id: string;
  start_time: string;
  end_time: string;
  healthcare_professional_id: string | null;
  reason: string | null;
  created_by: string;
  created_at: string;
};

type Appointment = {
  id: string;
  title: string;
  description: string | null;
  patient_id: string;
  patient_name?: string;
  healthcare_professional_id: string | null;
  healthcare_professional_name?: string;
  start_time: string;
  end_time: string;
  type: string;
  status: string;
  total_price: number | null;
};

type HealthcareProfessional = {
  id: string;
  name: string;
  specialty: string | null;
  is_active: boolean;
};

interface FullCalendarViewProps {
  appointments: Appointment[];
  appointmentBlocks?: AppointmentBlock[];
  healthcareProfessionals: HealthcareProfessional[];
  onAppointmentCreate: (selectInfo: DateSelectArg) => void;
  onAppointmentClick: (appointment: Appointment) => void;
  onAppointmentUpdate: (appointmentId: string, newStart: Date, newEnd: Date) => Promise<void>;
  loading?: boolean;
  selectedProfessional?: string;
  onSelectedProfessionalChange?: (professionalId: string) => void;
  onUnblockSlot?: (block: AppointmentBlock) => void;
}

const FullCalendarView: React.FC<FullCalendarViewProps> = ({
  appointments,
  appointmentBlocks = [],
  healthcareProfessionals,
  onAppointmentCreate,
  onAppointmentClick,
  onAppointmentUpdate,
  loading = false,
  selectedProfessional: externalSelectedProfessional,
  onSelectedProfessionalChange,
  onUnblockSlot
}) => {
  const calendarRef = useRef<FullCalendar>(null);
  const [internalSelectedProfessional, setInternalSelectedProfessional] = useState<string>('all');
  const [currentView, setCurrentView] = useState<string>('timeGridWeek');
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const { toast } = useToast();

  // Initialize component after data is loaded
  useEffect(() => {
    if (!loading && appointments && healthcareProfessionals) {
      setIsInitialized(true);
    }
  }, [loading, appointments, healthcareProfessionals]);

  // Use external selectedProfessional if provided, otherwise use internal state
  const selectedProfessional = externalSelectedProfessional ?? internalSelectedProfessional;
  
  const handleSelectedProfessionalChange = (professionalId: string) => {
    if (onSelectedProfessionalChange) {
      onSelectedProfessionalChange(professionalId);
    } else {
      setInternalSelectedProfessional(professionalId);
    }
  };

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      // Auto-switch to day view on mobile
      if (window.innerWidth < 768 && currentView === 'timeGridWeek') {
        setCurrentView('timeGridDay');
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [currentView]);

  // Filter appointment blocks by selected healthcare professional
  const filteredAppointmentBlocks = React.useMemo(() => {
    if (selectedProfessional === 'all') {
      return appointmentBlocks;
    }
    return appointmentBlocks.filter(block => block.healthcare_professional_id === selectedProfessional);
  }, [appointmentBlocks, selectedProfessional]);

  // Filter appointments by selected healthcare professional
  const filteredAppointments = React.useMemo(() => {
    if (selectedProfessional === 'all') {
      return appointments;
    }
    return appointments.filter(apt => apt.healthcare_professional_id === selectedProfessional);
  }, [appointments, selectedProfessional]);

  // Convert appointments and blocks to FullCalendar events
  const events: EventInput[] = React.useMemo(() => {
    const appointmentEvents = filteredAppointments.map(appointment => ({
      id: appointment.id,
      title: appointment.title,
      start: appointment.start_time,
      end: appointment.end_time,
      backgroundColor: getCalendarStatusColor(appointment.status as AppointmentStatus),
      borderColor: getCalendarStatusColor(appointment.status as AppointmentStatus),
      textColor: '#ffffff',
      extendedProps: {
        type: 'appointment',
        appointment,
        patient_name: appointment.patient_name,
        healthcare_professional_name: appointment.healthcare_professional_name,
        status: appointment.status,
        description: appointment.description
      }
    }));

    const blockEvents = filteredAppointmentBlocks.map(block => ({
      id: `block-${block.id}`,
      title: '🔒 Horário Bloqueado',
      start: block.start_time,
      end: block.end_time,
      backgroundColor: '#dc2626', // Red color for blocked slots
      borderColor: '#dc2626',
      textColor: '#ffffff',
      extendedProps: {
        type: 'block',
        block,
        reason: block.reason
      }
    }));

    return [...appointmentEvents, ...blockEvents];
  }, [filteredAppointments, filteredAppointmentBlocks]);

  const handleDateSelect = useCallback((selectInfo: DateSelectArg) => {
    if (onAppointmentCreate) {
      onAppointmentCreate(selectInfo);
    }
  }, [onAppointmentCreate]);

  const handleEventClick = useCallback((clickInfo: EventClickArg) => {
    const eventType = clickInfo.event.extendedProps.type;
    
    if (eventType === 'appointment') {
      const appointment = clickInfo.event.extendedProps.appointment as Appointment;
      if (onAppointmentClick && appointment) {
        onAppointmentClick(appointment);
      }
    } else if (eventType === 'block') {
      const block = clickInfo.event.extendedProps.block as AppointmentBlock;
      if (block && onUnblockSlot) {
        onUnblockSlot(block);
      }
    }
  }, [onAppointmentClick, onUnblockSlot]);

  const handleEventDrop = useCallback(async (dropInfo: EventDropArg) => {
    try {
      const appointmentId = dropInfo.event.id;
      const newStart = dropInfo.event.start;
      const newEnd = dropInfo.event.end;

      if (!appointmentId || !newStart || !newEnd || !onAppointmentUpdate) {
        dropInfo.revert();
        return;
      }

      await onAppointmentUpdate(appointmentId, newStart, newEnd);

      toast({
        title: "Sucesso!",
        description: "Consulta reagendada com sucesso.",
      });
    } catch (error) {
      console.error('Error updating appointment:', error);
      dropInfo.revert();
      toast({
        title: "Erro",
        description: "Erro ao reagendar consulta.",
        variant: "destructive"
      });
    }
  }, [onAppointmentUpdate, toast]);

  const handleEventResize = useCallback(async (resizeInfo: any) => {
    try {
      const appointmentId = resizeInfo.event.id;
      const newStart = resizeInfo.event.start;
      const newEnd = resizeInfo.event.end;

      if (!appointmentId || !newStart || !newEnd || !onAppointmentUpdate) {
        resizeInfo.revert();
        return;
      }

      await onAppointmentUpdate(appointmentId, newStart, newEnd);

      toast({
        title: "Sucesso!",
        description: "Duração da consulta atualizada com sucesso.",
      });
    } catch (error) {
      console.error('Error resizing appointment:', error);
      resizeInfo.revert();
      toast({
        title: "Erro",
        description: "Erro ao atualizar duração da consulta.",
        variant: "destructive"
      });
    }
  }, [onAppointmentUpdate, toast]);

  const handleViewChange = (view: string) => {
    setCurrentView(view);
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.changeView(view);
    }
  };

  const handleToday = () => {
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.today();
    }
  };

  const handlePrev = () => {
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.prev();
    }
  };

  const handleNext = () => {
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.next();
    }
  };

  if (loading || !isInitialized) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Calendar className="mr-2 h-5 w-5 text-primary" />
            Agenda Completa
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <CardTitle className="flex items-center text-lg">
            <Calendar className="mr-2 h-5 w-5 text-primary" />
            Agenda Completa
          </CardTitle>

          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
            {/* Healthcare Professional Filter */}
            <div className="flex items-center gap-2 min-w-0">
              <Filter className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <Select value={selectedProfessional} onValueChange={handleSelectedProfessionalChange}>
                <SelectTrigger className="w-[180px] sm:w-[200px]">
                  <SelectValue placeholder="Selecionar médico" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os médicos</SelectItem>
                  {healthcareProfessionals
                    .filter(prof => prof.is_active)
                    .map(professional => (
                      <SelectItem key={professional.id} value={professional.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{professional.name}</span>
                          {professional.specialty && (
                            <span className="text-xs text-muted-foreground">{professional.specialty}</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            {/* View Controls */}
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleToday}>
                Hoje
              </Button>
              <div className="flex items-center">
                <Button variant="ghost" size="sm" onClick={handlePrev}>‹</Button>
                <Button variant="ghost" size="sm" onClick={handleNext}>›</Button>
              </div>
              <Select value={currentView} onValueChange={handleViewChange}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="timeGridDay">Dia</SelectItem>
                  <SelectItem value="timeGridWeek">Semana</SelectItem>
                  <SelectItem value="dayGridMonth">Mês</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="w-full">
          {isInitialized && (
            <FullCalendar
              ref={calendarRef}
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              headerToolbar={false}
              initialView={currentView}
              editable={true}
              selectable={true}
              selectMirror={true}
              dayMaxEvents={true}
              weekends={true}
              events={events}
              select={handleDateSelect}
              eventClick={handleEventClick}
              eventDrop={handleEventDrop}
              eventResize={handleEventResize}
              locale="pt-br"
              timeZone="local"
              height="auto"
              eventContent={(eventInfo) => {
                const isAppointment = eventInfo.event.extendedProps.type === 'appointment';
                const patientName = eventInfo.event.extendedProps.patient_name;
                const professionalName = eventInfo.event.extendedProps.healthcare_professional_name;
                const status = eventInfo.event.extendedProps.status;
                const startTime = eventInfo.event.start;

                return (
                  <div className="p-1 text-xs overflow-hidden">
                    <div className="font-medium truncate">
                      {eventInfo.event.title}
                    </div>
                    {patientName && !isMobile && (
                      <div className="text-xs opacity-90 truncate">
                        {patientName}
                      </div>
                    )}
                    {professionalName && !isMobile && (
                      <div className="text-xs opacity-75 truncate">
                        {professionalName}
                      </div>
                    )}
                    {isMobile && startTime && (
                      <div className="text-xs opacity-75 truncate">
                        {format(startTime, 'HH:mm')}
                      </div>
                    )}
                  </div>
                );
              }}
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default FullCalendarView;