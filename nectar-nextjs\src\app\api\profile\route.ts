import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Get current user's profile data from users table
      const { data: user, error } = await supabase
        .from('users')
        .select('id, email, name, phone, role, is_active, created_at')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user profile:', error)
        return handleApiError(error)
      }

      return createApiResponse(user)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PATCH(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      const { name, phone } = body

      // Update user profile (only allow updating name and phone for now)
      const updateData: any = {}
      if (name !== undefined) updateData.name = name
      if (phone !== undefined) updateData.phone = phone

      const { data: user, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select('id, email, name, phone, role, is_active, created_at')
        .single()

      if (error) {
        console.error('Error updating user profile:', error)
        return handleApiError(error)
      }

      return createApiResponse(user, 'Profile updated successfully')
    } catch (error) {
      return handleApiError(error)
    }
  })
}
