import { NextRequest } from 'next/server'
import { withAuth, createApiR<PERSON>ponse, handleApiError } from '@/lib/api-utils'

interface UserAssociation {
  id: string
  accessor_user_id: string
  target_user_id: string
  association_type: 'secretary_to_doctor' | 'doctor_to_doctor'
  permissions?: Record<string, string[]> | null
  is_active: boolean
  created_by: string
  created_at: string
  updated_at: string
}

interface CreateAssociationRequest {
  accessor_user_id: string
  target_user_id: string
  association_type: 'secretary_to_doctor' | 'doctor_to_doctor'
  permissions?: Record<string, string[]>
}

interface UpdateAssociationRequest {
  permissions?: Record<string, string[]>
  is_active?: boolean
}

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const accessorUserId = searchParams.get('accessor_user_id')
      const targetUserId = searchParams.get('target_user_id')
      const associationType = searchParams.get('association_type')

      let query = supabase
        .from('user_associations')
        .select(`
          *,
          accessor_user:users!accessor_user_id(id, name, email, role),
          target_user:users!target_user_id(id, name, email, role),
          created_by_user:users!created_by(id, name, email)
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      // Apply filters
      if (accessorUserId) {
        query = query.eq('accessor_user_id', accessorUserId)
      }
      if (targetUserId) {
        query = query.eq('target_user_id', targetUserId)
      }
      if (associationType) {
        query = query.eq('association_type', associationType)
      }

      const { data: associations, error } = await query

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(associations || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user is admin using simplified role system
      const { data: currentUser, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (userError) {
        return handleApiError(userError)
      }

      const isAdmin = currentUser?.role === 'admin'

      const body: CreateAssociationRequest = await request.json()
      const { accessor_user_id, target_user_id, association_type, permissions } = body

      if (!accessor_user_id || !target_user_id || !association_type) {
        return createApiResponse(null, 'accessor_user_id, target_user_id, and association_type are required', 400)
      }

      // Validate association type
      if (!['secretary_to_doctor', 'doctor_to_doctor'].includes(association_type)) {
        return createApiResponse(null, 'Invalid association_type', 400)
      }

      // Check if user can create this association
      if (!isAdmin && userId !== accessor_user_id && userId !== target_user_id) {
        return createApiResponse(null, 'You can only create associations you are part of', 403)
      }

      // Validate user roles for association type
      if (association_type === 'secretary_to_doctor') {
        // Check if accessor is secretary and target is doctor
        const { data: accessorUser } = await supabase
          .from('users')
          .select('role')
          .eq('id', accessor_user_id)
          .single()

        const { data: targetUser } = await supabase
          .from('users')
          .select('role')
          .eq('id', target_user_id)
          .single()

        const accessorIsSecretary = accessorUser?.role === 'secretary'
        const targetIsHealthcareProfessional = targetUser?.role === 'healthcare_professional'

        if (!accessorIsSecretary || !targetIsHealthcareProfessional) {
          return createApiResponse(null, 'For secretary_to_doctor association, accessor must be secretary and target must be healthcare_professional', 400)
        }
      }

      if (association_type === 'doctor_to_doctor') {
        // Check if both are healthcare_professionals
        const { data: accessorUser } = await supabase
          .from('users')
          .select('role')
          .eq('id', accessor_user_id)
          .single()

        const { data: targetUser } = await supabase
          .from('users')
          .select('role')
          .eq('id', target_user_id)
          .single()

        const accessorIsHealthcareProfessional = accessorUser?.role === 'healthcare_professional'
        const targetIsHealthcareProfessional = targetUser?.role === 'healthcare_professional'

        if (!accessorIsHealthcareProfessional || !targetIsHealthcareProfessional) {
          return createApiResponse(null, 'For doctor_to_doctor association, both users must be healthcare_professionals', 400)
        }
      }

      // Create the association
      const { data: association, error } = await supabase
        .from('user_associations')
        .insert({
          accessor_user_id,
          target_user_id,
          association_type,
          permissions: permissions || null,
          created_by: userId,
          is_active: true
        })
        .select(`
          *,
          accessor_user:users!accessor_user_id(id, name, email, role),
          target_user:users!target_user_id(id, name, email, role),
          created_by_user:users!created_by(id, name, email)
        `)
        .single()

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          return createApiResponse(null, 'Association already exists', 409)
        }
        return handleApiError(error)
      }

      return createApiResponse(association, 'Association created successfully', 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PATCH(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const associationId = searchParams.get('id')

      if (!associationId) {
        return createApiResponse(null, 'Association ID is required', 400)
      }

      const body: UpdateAssociationRequest = await request.json()

      // Check if user can update this association
      const { data: association, error: fetchError } = await supabase
        .from('user_associations')
        .select('*')
        .eq('id', associationId)
        .single()

      if (fetchError) {
        return handleApiError(fetchError)
      }

      if (!association) {
        return createApiResponse(null, 'Association not found', 404)
      }

      // Check permissions using simplified role system
      const { data: currentUser, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (userError) {
        return handleApiError(userError)
      }

      const isAdmin = currentUser?.role === 'admin'
      const canUpdate = isAdmin || association.created_by === userId

      if (!canUpdate) {
        return createApiResponse(null, 'You can only update associations you created', 403)
      }

      // Update the association
      const { data: updatedAssociation, error } = await supabase
        .from('user_associations')
        .update(body)
        .eq('id', associationId)
        .select(`
          *,
          accessor_user:users!accessor_user_id(id, name, email, role),
          target_user:users!target_user_id(id, name, email, role),
          created_by_user:users!created_by(id, name, email)
        `)
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(updatedAssociation, 'Association updated successfully')
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function DELETE(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const associationId = searchParams.get('id')

      if (!associationId) {
        return createApiResponse(null, 'Association ID is required', 400)
      }

      // Check if user can delete this association
      const { data: association, error: fetchError } = await supabase
        .from('user_associations')
        .select('*')
        .eq('id', associationId)
        .single()

      if (fetchError) {
        return handleApiError(fetchError)
      }

      if (!association) {
        return createApiResponse(null, 'Association not found', 404)
      }

      // Check permissions using simplified role system
      const { data: currentUser, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (userError) {
        return handleApiError(userError)
      }

      const isAdmin = currentUser?.role === 'admin'
      const canDelete = isAdmin || association.created_by === userId

      if (!canDelete) {
        return createApiResponse(null, 'You can only delete associations you created', 403)
      }

      // Delete the association
      const { error } = await supabase
        .from('user_associations')
        .delete()
        .eq('id', associationId)

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(null, 'Association deleted successfully')
    } catch (error) {
      return handleApiError(error)
    }
  })
}
