import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const filePath = searchParams.get('path')

      if (!filePath) {
        return createApiResponse(null, 'Caminho do arquivo é obrigatório', 400)
      }

      // Verify the file belongs to the user by checking the path structure
      // Path should be: userId/appointmentId/filename
      const pathParts = filePath.split('/')
      if (pathParts.length !== 3 || pathParts[0] !== userId) {
        return createApiResponse(null, 'Acesso negado', 403)
      }

      // Get file from Supabase Storage
      const { data, error } = await supabase.storage
        .from('consultation-attachments')
        .download(filePath)

      if (error) {
        console.error('Storage download error:', error)
        return handleApiError(error)
      }

      if (!data) {
        return createApiResponse(null, 'Arquivo não encontrado', 404)
      }

      // Get file info to set proper headers
      const { data: fileInfo, error: infoError } = await supabase
        .from('consultation_attachments')
        .select('file_name, file_type')
        .eq('file_path', filePath)
        .eq('user_id', userId)
        .single()

      if (infoError || !fileInfo) {
        return createApiResponse(null, 'Informações do arquivo não encontradas', 404)
      }

      // Create response with proper headers
      const response = new Response(data, {
        status: 200,
        headers: {
          'Content-Type': fileInfo.file_type,
          'Content-Disposition': `attachment; filename="${fileInfo.file_name}"`,
          'Cache-Control': 'private, max-age=3600'
        }
      })

      return response
    } catch (error) {
      return handleApiError(error)
    }
  })
}
