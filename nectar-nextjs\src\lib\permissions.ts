import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/supabase'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for server-side operations
const supabaseAdmin = createClient<Database>(supabaseUrl, supabaseServiceKey)

export type Permission = {
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete';
};

export type Role = 'admin' | 'healthcare_professional' | 'secretary' | 'assistant';

/**
 * Check if a user has permission to access a resource based on user associations
 */
export async function hasPermission(
  userId: string, 
  resource: string, 
  action: 'create' | 'read' | 'update' | 'delete'
): Promise<boolean> {
  try {
    // Get user role from simplified system
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    if (userError || !user) {
      return false
    }

    // Admin has all permissions
    if (user.role === 'admin') {
      return true
    }

    // Healthcare professionals have full permissions for their own resources
    if (user.role === 'healthcare_professional') {
      return hasHealthcareProfessionalPermission(resource, action)
    }

    // For secretaries, check user associations to see what they can access
    if (user.role === 'secretary') {
      return await hasSecretaryPermission(userId, resource, action)
    }

    // Assistants have read-only access to basic resources
    if (user.role === 'assistant') {
      return hasAssistantPermission(resource, action)
    }

    return false
  } catch (error) {
    console.error('Error in hasPermission:', error)
    return false
  }
}

/**
 * Check secretary permissions based on user associations
 */
async function hasSecretaryPermission(
  userId: string,
  resource: string,
  action: 'create' | 'read' | 'update' | 'delete'
): Promise<boolean> {
  try {
    // Secretaries can create, read, and update appointments and patients for doctors they're associated with
    if (resource === 'appointments' && ['create', 'read', 'update'].includes(action)) {
      return true
    }
    
    if (resource === 'patients' && ['create', 'read', 'update'].includes(action)) {
      return true
    }

    return false
  } catch (error) {
    console.error('Error checking secretary permissions:', error)
    return false
  }
}

/**
 * Healthcare professional permissions
 */
function hasHealthcareProfessionalPermission(
  resource: string,
  action: 'create' | 'read' | 'update' | 'delete'
): boolean {
  // Healthcare professionals have full access to their own data
  const allowedResources = ['appointments', 'patients', 'medical_records']
  return allowedResources.includes(resource)
}

/**
 * Assistant permissions (read-only)
 */
function hasAssistantPermission(
  resource: string,
  action: 'create' | 'read' | 'update' | 'delete'
): boolean {
  // Assistants have read-only access
  return action === 'read' && ['appointments', 'patients'].includes(resource)
}

/**
 * Check if a user has any of the specified roles
 */
export async function hasRole(userId: string, roles: Role[]): Promise<boolean> {
  try {
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    if (error || !user) {
      console.error('Error checking roles:', error)
      return false
    }

    return roles.includes(user.role as Role)
  } catch (error) {
    console.error('Error in hasRole:', error)
    return false
  }
}

/**
 * Get all permissions for a user based on their role and associations
 */
export async function getUserPermissions(userId: string): Promise<Permission[]> {
  try {
    // Get user role from simplified system
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    if (userError || !user) {
      return []
    }

    // Return permissions based on role
    return DEFAULT_PERMISSIONS[user.role as Role] || []
  } catch (error) {
    console.error('Error in getUserPermissions:', error)
    return []
  }
}

/**
 * Check if a user can access a specific healthcare professional's data
 */
export async function canAccessHealthcareProfessional(
  userId: string,
  healthcareProfessionalId: string
): Promise<boolean> {
  try {
    // Get user role
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    if (userError || !user) {
      return false
    }

    // Admin can access all
    if (user.role === 'admin') {
      return true
    }

    // Healthcare professionals can access their own data
    if (user.role === 'healthcare_professional') {
      const { data: professional, error: profError } = await supabaseAdmin
        .from('healthcare_professionals')
        .select('id')
        .eq('user_id', userId)
        .eq('id', healthcareProfessionalId)
        .single()

      return !profError && !!professional
    }

    // Secretaries can access healthcare professionals they're associated with
    if (user.role === 'secretary') {
      const { data: professional, error: profError } = await supabaseAdmin
        .from('healthcare_professionals')
        .select('user_id')
        .eq('id', healthcareProfessionalId)
        .single()

      if (profError || !professional) {
        return false
      }

      const { data: association, error: assocError } = await supabaseAdmin
        .from('user_associations')
        .select('id')
        .eq('accessor_user_id', userId)
        .eq('target_user_id', professional.user_id)
        .eq('association_type', 'secretary_doctor')
        .eq('is_active', true)
        .single()

      return !assocError && !!association
    }

    return false
  } catch (error) {
    console.error('Error checking healthcare professional access:', error)
    return false
  }
}

/**
 * Check if a user is an admin
 */
export async function isAdmin(userId: string): Promise<boolean> {
  return hasRole(userId, ['admin'])
}

/**
 * Middleware function to check permissions for API routes
 */
export function requirePermission(resource: string, action: 'create' | 'read' | 'update' | 'delete') {
  return async (userId: string): Promise<boolean> => {
    return hasPermission(userId, resource, action)
  }
}

/**
 * Middleware function to check roles for API routes
 */
export function requireRole(roles: Role[]) {
  return async (userId: string): Promise<boolean> => {
    return hasRole(userId, roles)
  }
}

/**
 * Updated default permissions for each role based on association model
 */
export const DEFAULT_PERMISSIONS: Record<Role, Permission[]> = {
  admin: [
    // Full access to everything
    { resource: 'appointments', action: 'create' },
    { resource: 'appointments', action: 'read' },
    { resource: 'appointments', action: 'update' },
    { resource: 'appointments', action: 'delete' },
    { resource: 'patients', action: 'create' },
    { resource: 'patients', action: 'read' },
    { resource: 'patients', action: 'update' },
    { resource: 'patients', action: 'delete' },
    { resource: 'healthcare_professionals', action: 'create' },
    { resource: 'healthcare_professionals', action: 'read' },
    { resource: 'healthcare_professionals', action: 'update' },
    { resource: 'healthcare_professionals', action: 'delete' },
    { resource: 'users', action: 'create' },
    { resource: 'users', action: 'read' },
    { resource: 'users', action: 'update' },
    { resource: 'users', action: 'delete' },
    { resource: 'user_associations', action: 'create' },
    { resource: 'user_associations', action: 'read' },
    { resource: 'user_associations', action: 'update' },
    { resource: 'user_associations', action: 'delete' },
    { resource: 'medical_records', action: 'create' },
    { resource: 'medical_records', action: 'read' },
    { resource: 'medical_records', action: 'update' },
    { resource: 'medical_records', action: 'delete' },
    { resource: 'settings', action: 'read' },
    { resource: 'settings', action: 'update' },
  ],
  healthcare_professional: [
    // Can manage their own appointments, patients and medical records
    { resource: 'appointments', action: 'create' },
    { resource: 'appointments', action: 'read' },
    { resource: 'appointments', action: 'update' },
    { resource: 'appointments', action: 'delete' },
    { resource: 'patients', action: 'create' },
    { resource: 'patients', action: 'read' },
    { resource: 'patients', action: 'update' },
    { resource: 'patients', action: 'delete' },
    { resource: 'medical_records', action: 'create' },
    { resource: 'medical_records', action: 'read' },
    { resource: 'medical_records', action: 'update' },
    { resource: 'medical_records', action: 'delete' },
  ],
  secretary: [
    // Can manage appointments and patients for associated doctors
    // Note: actual access is controlled by user_associations table
    { resource: 'appointments', action: 'create' },
    { resource: 'appointments', action: 'read' },
    { resource: 'appointments', action: 'update' },
    { resource: 'patients', action: 'create' },
    { resource: 'patients', action: 'read' },
    { resource: 'patients', action: 'update' },
    { resource: 'healthcare_professionals', action: 'read' }, // Can see associated doctors
  ],
  assistant: [
    // Read-only access to appointments and patients
    { resource: 'appointments', action: 'read' },
    { resource: 'patients', action: 'read' },
    { resource: 'healthcare_professionals', action: 'read' },
  ],
};
