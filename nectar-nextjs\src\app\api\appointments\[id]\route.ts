import { NextRequest, NextResponse } from 'next/server'
import { withAuth, withAuthAndPermission, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesUpdate } from '@/types/supabase'

type Appointment = Tables<'appointments'>
type AppointmentUpdate = TablesUpdate<'appointments'>

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      console.log('🔍 GET Appointment - User ID:', userId)
      
      const { data: appointment, error } = await supabase
        .from('appointments')
        .select(`
          *,
          patients(name),
          healthcare_professionals(name, specialty)
        `)
        .eq('id', id)
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(appointment)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      console.log('🔍 PUT Appointment - User ID:', userId)
      
      const body = await request.json()
      console.log('📝 Update data:', body)

      const updateData: AppointmentUpdate = {
        ...body,
        total_price: body.price ? parseFloat(body.price) : body.total_price ? parseFloat(body.total_price) : null,
      }

      if (body.start_time) {
        updateData.start_time = new Date(body.start_time).toISOString()
      }

      if (body.end_time) {
        updateData.end_time = new Date(body.end_time).toISOString()
      }

      console.log('🔄 Final update data:', updateData)

      const { data: appointment, error } = await supabase
        .from('appointments')
        .update(updateData)
        .eq('id', id)
        .select(`
          *,
          patients(name),
          healthcare_professionals(name, specialty)
        `)
        .single()

      if (error) {
        console.error('❌ Update error:', error)
        return handleApiError(error)
      }

      console.log('✅ Appointment updated successfully')
      return createApiResponse(appointment)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      const { error } = await supabase
        .from('appointments')
        .delete()
        .eq('id', id)
        .eq('created_by', userId)

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse({ success: true })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
