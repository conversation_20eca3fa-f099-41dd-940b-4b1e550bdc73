import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import { 
  resolveHealthcareProfessionalId, 
  fetchClinicSettings, 
  upsertClinicSettings,
  DEFAULT_CLINIC_SETTINGS,
  type ClinicSettings,
  type ClinicSettingsInsert,
  type ClinicSettingsUpdate
} from '@/lib/clinic-settings-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const url = new URL(request.url)
      const targetUserId = url.searchParams.get('user_id')
      const healthcareProfessionalId = url.searchParams.get('healthcare_professional_id')

      // Resolve which healthcare professional to fetch settings for
      const result = await resolveHealthcareProfessionalId(
        supabase, 
        userId, 
        targetUserId, 
        healthcareProfessionalId
      )

      if (!result.success) {
        // If user is not a healthcare professional, return default settings
        if (result.error?.code === 'PGRST116') {
          console.log('ℹ️ User is not a healthcare professional, returning default settings');
          return createApiResponse(DEFAULT_CLINIC_SETTINGS)
        }
        return handleApiError(result.error)
      }

      // Fetch clinic settings
      const settings = await fetchClinicSettings(supabase, result.healthcareProfessionalId!)
      return createApiResponse(settings)

    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      const url = new URL(request.url)
      const targetUserId = url.searchParams.get('user_id')
      const healthcareProfessionalId = url.searchParams.get('healthcare_professional_id')

      // Resolve which healthcare professional to create settings for
      const result = await resolveHealthcareProfessionalId(
        supabase, 
        userId, 
        targetUserId, 
        healthcareProfessionalId
      )

      if (!result.success) {
        return handleApiError(result.error)
      }

      // Create new clinic settings
      const settings = await upsertClinicSettings(
        supabase, 
        result.healthcareProfessionalId!, 
        body, 
        false
      )

      return createApiResponse(settings, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PUT(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      const url = new URL(request.url)
      const targetUserId = url.searchParams.get('user_id')
      const healthcareProfessionalId = url.searchParams.get('healthcare_professional_id')

      // Resolve which healthcare professional to update settings for
      const result = await resolveHealthcareProfessionalId(
        supabase, 
        userId, 
        targetUserId, 
        healthcareProfessionalId
      )

      if (!result.success) {
        return handleApiError(result.error)
      }

      // Update clinic settings
      const settings = await upsertClinicSettings(
        supabase, 
        result.healthcareProfessionalId!, 
        body, 
        true
      )

      return createApiResponse(settings)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
