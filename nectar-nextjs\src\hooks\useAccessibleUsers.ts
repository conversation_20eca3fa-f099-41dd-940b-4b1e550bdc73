"use client"

import { useState, useEffect } from 'react'
import { makeAuthenticatedRequest } from '@/lib/api-client'

interface AccessibleUser {
  user_id: string
  email: string
  name: string
  phone?: string
  role: string
  association_type: string | null
  permissions: Record<string, string[]> | null
  is_own_data: boolean
  is_active: boolean
  created_at: string
}

interface UseAccessibleUsersOptions {
  roleFilter?: string
  includeInactive?: boolean
  autoFetch?: boolean
}

interface UseAccessibleUsersReturn {
  users: AccessibleUser[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  checkAccess: (targetUserId: string, resource?: string, action?: string) => Promise<boolean>
}

export function useAccessibleUsers(options: UseAccessibleUsersOptions = {}): UseAccessibleUsersReturn {
  const { roleFilter, includeInactive = false, autoFetch = true } = options
  
  const [users, setUsers] = useState<AccessibleUser[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchUsers = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (roleFilter) params.append('role', roleFilter)
      if (includeInactive) params.append('include_inactive', 'true')

      const response = await makeAuthenticatedRequest(`/api/accessible-users?${params.toString()}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to fetch accessible users')
      }

      const result = await response.json()
      setUsers(result.data || [])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Error fetching accessible users:', err)
    } finally {
      setLoading(false)
    }
  }

  const checkAccess = async (targetUserId: string, resource?: string, action?: string): Promise<boolean> => {
    try {
      const response = await makeAuthenticatedRequest('/api/accessible-users', {
        method: 'POST',
        body: JSON.stringify({
          target_user_id: targetUserId,
          resource,
          action
        })
      })

      if (!response.ok) {
        return false
      }

      const result = await response.json()
      return result.data?.has_access || false
    } catch (err) {
      console.error('Error checking access:', err)
      return false
    }
  }

  useEffect(() => {
    if (autoFetch) {
      fetchUsers()
    }
  }, [roleFilter, includeInactive, autoFetch])

  return {
    users,
    loading,
    error,
    refetch: fetchUsers,
    checkAccess
  }
}

// Hook específico para healthcare professionals acessíveis
export function useAccessibleHealthcareProfessionals(includeInactive = false) {
  return useAccessibleUsers({ 
    roleFilter: 'healthcare_professional', 
    includeInactive 
  })
}

// Compatibilidade com código existente
export function useAccessibleDoctors(includeInactive = false) {
  return useAccessibleHealthcareProfessionals(includeInactive)
}

// Hook específico para secretárias acessíveis
export function useAccessibleSecretaries(includeInactive = false) {
  return useAccessibleUsers({ 
    roleFilter: 'secretary', 
    includeInactive 
  })
}

// Hook para verificar se o usuário atual tem acesso a um usuário específico
export function useUserAccess(targetUserId: string | null, resource?: string, action?: string) {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!targetUserId) {
      setHasAccess(null)
      return
    }

    const checkAccess = async () => {
      try {
        setLoading(true)
        const response = await makeAuthenticatedRequest('/api/accessible-users', {
          method: 'POST',
          body: JSON.stringify({
            target_user_id: targetUserId,
            resource,
            action
          })
        })

        if (response.ok) {
          const result = await response.json()
          setHasAccess(result.data?.has_access || false)
        } else {
          setHasAccess(false)
        }
      } catch (err) {
        console.error('Error checking user access:', err)
        setHasAccess(false)
      } finally {
        setLoading(false)
      }
    }

    checkAccess()
  }, [targetUserId, resource, action])

  return { hasAccess, loading }
}
