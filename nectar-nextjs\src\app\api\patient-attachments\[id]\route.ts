import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables } from '@/types/supabase'

type PatientAttachment = Tables<'patient_attachments'>

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      // Get attachment metadata
      const { data: attachment, error } = await supabase
        .from('patient_attachments')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .single()

      if (error) {
        return handleApiError(error)
      }

      // Get signed URL for file download
      const { data: signedUrlData, error: urlError } = await supabase.storage
        .from('patient-files')
        .createSignedUrl(attachment.file_path, 3600) // 1 hour expiry

      if (urlError) {
        return handleApiError(urlError)
      }

      return createApiResponse({
        ...attachment,
        download_url: signedUrlData.signedUrl
      })
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      // Get attachment metadata first
      const { data: attachment, error: fetchError } = await supabase
        .from('patient_attachments')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .single()

      if (fetchError) {
        return handleApiError(fetchError)
      }

      // Delete file from storage
      const { error: storageError } = await supabase.storage
        .from('patient-files')
        .remove([attachment.file_path])

      if (storageError) {
        console.error('Error deleting file from storage:', storageError)
        // Continue with database deletion even if storage deletion fails
      }

      // Delete attachment record from database
      const { error } = await supabase
        .from('patient_attachments')
        .delete()
        .eq('id', id)
        .eq('user_id', userId)

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse({ message: 'Attachment deleted successfully' })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
