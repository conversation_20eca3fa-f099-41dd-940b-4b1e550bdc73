import { NextRequest } from 'next/server'
import { withAuth, withAuthAndPermission, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type Appointment = Tables<'appointments'>
type AppointmentInsert = TablesInsert<'appointments'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const date = searchParams.get('date')
      const patientId = searchParams.get('patient_id')
      const healthcareProfessionalId = searchParams.get('healthcare_professional_id')

      // Get accessible users first
      const { data: accessibleUsers, error: accessError } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      if (accessError) {
        return handleApiError(accessError)
      }

      const accessibleUserIds = accessibleUsers?.map((user: any) => user.user_id) || []

      if (accessibleUserIds.length === 0) {
        return createApiResponse([])
      }

      // Check if current user is a doctor by looking for healthcare_professional record
      const { data: currentUserProfessional, error: profError } = await supabase
        .from('healthcare_professionals')
        .select('id')
        .eq('user_id', userId)
        .single()

      console.log('🏥 Current user professional:', { currentUserProfessional, profError, userId })

      let appointments: any[] = []

      // If specific healthcare professional is requested and user has access
      if (healthcareProfessionalId) {
        console.log('📋 Filtering by healthcare_professional_id:', healthcareProfessionalId)
        
        // Verify user has access to this healthcare professional
        const hasAccess = currentUserProfessional?.id === healthcareProfessionalId || 
                         accessibleUserIds.length > 0; // Admins and secretaries can access
        
        if (hasAccess) {
          const { data, error } = await supabase
            .from('appointments')
            .select(`
              *,
              patients(name),
              healthcare_professionals(name, specialty)
            `)
            .eq('healthcare_professional_id', healthcareProfessionalId)

          if (error) {
            console.error('Error fetching filtered appointments:', error)
            return handleApiError(error)
          }

          appointments = data || []
          console.log('📋 Filtered appointments found:', appointments.length)
        }
      } else if (currentUserProfessional && !profError) {
        // For doctors: get ALL appointments assigned to them via healthcare_professional_id
        // This includes appointments created by secretaries or other users
        console.log('📋 Fetching appointments for doctor - professionalId:', currentUserProfessional.id)

        const { data, error } = await supabase
          .from('appointments')
          .select(`
            *,
            patients(name),
            healthcare_professionals(name, specialty)
          `)
          .eq('healthcare_professional_id', currentUserProfessional.id)

        if (error) {
          console.error('Error fetching doctor appointments:', error)
          return handleApiError(error)
        }

        appointments = data || []
        console.log('📋 Doctor appointments found:', appointments.length)

        // Debug log for the specific doctor
        if (userId === 'f8a54e36-d6d4-4a74-a0cc-383cffee47d3') {
          console.log('🔍 Debug for Erico - appointments:', appointments.map(a => ({
            id: a.id,
            title: a.title,
            start_time: a.start_time,
            healthcare_professional_id: a.healthcare_professional_id
          })))
        }
      } else {
        // For non-doctors (secretaries, admins): show appointments they have access to via created_by
        console.log('📋 Using created_by filter for non-doctor:', accessibleUserIds)
        const { data, error } = await supabase
          .from('appointments')
          .select(`
            *,
            patients(name),
            healthcare_professionals(name, specialty)
          `)
          .in('created_by', accessibleUserIds)

        if (error) {
          console.error('Error fetching user appointments:', error)
          return handleApiError(error)
        }

        appointments = data || []
        console.log('📋 User appointments found:', appointments.length)
      }

      // Apply date filter if provided
      if (date) {
        // Parse date in local timezone to avoid UTC conversion issues
        const [year, month, day] = date.split('-').map(Number)
        const startDate = new Date(year, month - 1, day, 0, 0, 0, 0)
        const endDate = new Date(year, month - 1, day + 1, 0, 0, 0, 0)

        // Filter appointments by date
        appointments = appointments.filter(appointment => {
          const appointmentDate = new Date(appointment.start_time)
          return appointmentDate >= startDate && appointmentDate < endDate
        })
      }

      if (patientId) {
        appointments = appointments.filter(appointment => appointment.patient_id === patientId)
      }

      // Sort appointments - ascending for date-specific queries (agenda), descending for patient history
      const orderDirection = date ? 1 : -1
      appointments.sort((a, b) => {
        const dateA = new Date(a.start_time)
        const dateB = new Date(b.start_time)
        return orderDirection * (dateA.getTime() - dateB.getTime())
      })

      console.log('📅 Appointments found:', appointments?.length || 0, 'for user:', userId)
      if (appointments && appointments.length > 0) {
        console.log('📋 First appointment sample:', {
          id: appointments[0].id,
          created_by: appointments[0].created_by,
          healthcare_professional_id: appointments[0].healthcare_professional_id,
          title: appointments[0].title
        })
      }

      return createApiResponse(appointments)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()

      // Validate required fields
      if (!body.start_time || !body.end_time) {
        return createApiResponse(null, 'Horários de início e fim são obrigatórios', 400)
      }

      const startTime = new Date(body.start_time).toISOString()
      const endTime = new Date(body.end_time).toISOString()

      // Validate clinic working hours
      if (body.healthcare_professional_id) {
        // Get clinic settings for the healthcare professional
        const { data: professional, error: profError } = await supabase
          .from('healthcare_professionals')
          .select('user_id')
          .eq('id', body.healthcare_professional_id)
          .single()

        if (!profError && professional) {
          // Get clinic settings for this professional's user
          const { data: clinicSettings, error: settingsError } = await supabase
            .from('clinic_settings')
            .select('weekly_schedule')
            .eq('user_id', professional.user_id)
            .single()

          if (!settingsError && clinicSettings && clinicSettings.weekly_schedule) {
            const appointmentDate = new Date(body.start_time)
            const dayOfWeek = appointmentDate.getDay() // 0 = Sunday, 1 = Monday, etc.
            
            // Map day of week to our schedule keys
            const dayMapping = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
            const dayKey = dayMapping[dayOfWeek]
            
            const daySchedule = clinicSettings.weekly_schedule[dayKey]
            
            if (!daySchedule || !daySchedule.enabled) {
              return createApiResponse(
                null, 
                'A clínica não funciona neste dia da semana.',
                400
              )
            }

            // Extract time from appointment start and end
            const appointmentStartTime = appointmentDate.toTimeString().substr(0, 5) // HH:MM format
            const appointmentEndTime = new Date(body.end_time).toTimeString().substr(0, 5)
            
            const workingStart = daySchedule.working_hours_start || '08:00'
            const workingEnd = daySchedule.working_hours_end || '18:00'

            // Check if appointment is within working hours
            if (appointmentStartTime < workingStart || appointmentEndTime > workingEnd) {
              return createApiResponse(
                null, 
                `Este horário está fora do funcionamento da clínica. Horário permitido: ${workingStart} às ${workingEnd}.`,
                400
              )
            }

            // Check if appointment conflicts with break intervals
            if (daySchedule.break_intervals && daySchedule.break_intervals.length > 0) {
              for (const breakInterval of daySchedule.break_intervals) {
                const breakStart = breakInterval.start
                const breakEnd = breakInterval.end
                
                // Check if appointment overlaps with break interval
                if (
                  (appointmentStartTime >= breakStart && appointmentStartTime < breakEnd) ||
                  (appointmentEndTime > breakStart && appointmentEndTime <= breakEnd) ||
                  (appointmentStartTime <= breakStart && appointmentEndTime >= breakEnd)
                ) {
                  return createApiResponse(
                    null, 
                    `Este horário conflita com um intervalo de não trabalho (${breakStart} às ${breakEnd}).`,
                    400
                  )
                }
              }
            }
          }
        }
      }

      // Check for conflicts with existing appointments
      if (body.healthcare_professional_id) {
        const { data: existingAppointments } = await supabase
          .from('appointments')
          .select('id, start_time, end_time, status')
          .eq('healthcare_professional_id', body.healthcare_professional_id)
          .neq('status', 'cancelled')
          .lt('start_time', endTime)
          .gt('end_time', startTime)

        if (existingAppointments && existingAppointments.length > 0) {
          return createApiResponse(
            null, 
            'Já existe uma consulta agendada neste horário. Escolha outro horário.',
            409
          )
        }

        // Check for conflicts with blocked time slots
        const { data: existingBlocks } = await supabase
          .from('appointment_blocks')
          .select('id, start_time, end_time')
          .eq('healthcare_professional_id', body.healthcare_professional_id)
          .lt('start_time', endTime)
          .gt('end_time', startTime)

        if (existingBlocks && existingBlocks.length > 0) {
          return createApiResponse(
            null, 
            'Este horário está bloqueado e não está disponível para agendamentos.',
            409
          )
        }
      }

      // Get accessible users to validate target user
      const { data: accessibleUsers, error: accessError } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      if (accessError) {
        return handleApiError(accessError)
      }

      const accessibleUserIds = accessibleUsers?.map((user: any) => user.user_id) || []

      // Determine the target created_by for the appointment
      let targetUserId = userId // Default to current user (who is creating the appointment)

      // If a healthcare professional is specified, we still create as the current user
      // but assign to the healthcare professional
      if (body.healthcare_professional_id) {
        const { data: professional, error: profError } = await supabase
          .from('healthcare_professionals')
          .select('user_id')
          .eq('id', body.healthcare_professional_id)
          .single()

        if (!profError && professional && accessibleUserIds.includes(professional.user_id)) {
          // We keep created_by as current user, but log the professional assignment
          console.log('🎯 Assigning appointment to professional:', professional.user_id, 'created by:', userId)
        }
      }

      // Legacy support: If a specific doctor_id is specified and user has access to them, use that for assignment
      if (body.doctor_id && accessibleUserIds.includes(body.doctor_id)) {
        console.log('🎯 Legacy doctor_id assignment:', body.doctor_id, 'created by:', userId)
      }

      console.log('📝 Creating appointment created by:', targetUserId, 'for professional:', body.healthcare_professional_id)

      const appointmentData: AppointmentInsert = {
        ...body,
        created_by: targetUserId, // Always the user who creates the appointment
        status: body.status || 'scheduled',
        start_time: startTime,
        end_time: endTime,
        healthcare_professional_id: body.healthcare_professional_id || null,
        total_price: body.total_price || null,
        recurrence_rule: body.has_recurrence ? JSON.stringify({
          type: body.recurrence_type,
          interval: body.recurrence_interval,
          days: body.recurrence_days,
          end_type: body.recurrence_end_type,
          end_date: body.recurrence_end_date,
          count: body.recurrence_count
        }) : null,
        recurrence_end_date: body.recurrence_end_date || null
      }

      // Remove doctor_id from appointment data as it's not a column in appointments table
      delete (appointmentData as any).doctor_id

      // Create appointment
      const { data: appointment, error: appointmentError } = await supabase
        .from('appointments')
        .insert(appointmentData)
        .select(`
          *,
          patients!inner(name),
          healthcare_professionals(name, specialty)
        `)
        .single()

      if (appointmentError) {
        return handleApiError(appointmentError)
      }

      return createApiResponse(appointment, 'Consulta agendada com sucesso', 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
