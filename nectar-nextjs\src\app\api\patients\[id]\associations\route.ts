import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params

  return withAuth(request, async (userId, supabase) => {
    try {
      console.log('🔍 Fetching associations for patient:', id, 'by user:', userId);

      // Check if user has access to this patient
      const { data: accessiblePatients, error: accessError } = await supabase
        .rpc('get_accessible_patients', { current_user_id: userId })

      if (accessError) {
        console.error('❌ Access check error:', accessError);
        return handleApiError(accessError)
      }

      const hasAccess = accessiblePatients?.some((p: any) => p.id === id)
      if (!hasAccess) {
        console.error('❌ Access denied for patient:', id, 'user:', userId);
        return createApiResponse(null, 'Patient not found or access denied', 404)
      }

      // Get patient associations
      const { data: associations, error } = await supabase
        .from('patient_healthcare_professional_associations')
        .select(`
          id,
          healthcare_professional_id,
          healthcare_professionals(
            id,
            name,
            specialty,
            user_id
          )
        `)
        .eq('patient_id', id)

      if (error) {
        console.error('❌ Database error:', error);
        return handleApiError(error)
      }

      console.log('✅ Found', associations?.length || 0, 'associations for patient:', id);
      return createApiResponse(associations || [])
    } catch (error) {
      console.error('❌ GET associations error:', error);
      return handleApiError(error)
    }
  })
}
