import { createClient } from '@/lib/supabase/client'
import { throttledFetch } from '@/lib/request-throttle'

export async function makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
  const supabase = createClient()

  try {
    const { data: { session } } = await supabase.auth.getSession()

    // Don't set Content-Type for FormData - let the browser set it automatically
    const isFormData = options.body instanceof FormData

    const headers: Record<string, string> = {
      ...options.headers,
    }

    // Only set Content-Type for non-FormData requests
    if (!isFormData) {
      headers['Content-Type'] = 'application/json'
    }

    // Add authorization header if we have a session
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`
    }

    // Use throttled fetch to prevent rate limiting
    return await throttledFetch(url, {
      ...options,
      headers,
      credentials: 'include', // Include cookies
    })
  } catch (error) {
    console.error('[API CLIENT] Request failed:', error);
    throw error;
  }
}
