import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/supabase'

// Create admin client with service role key for admin operations
export function createAdminClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

  console.log('Admin client config:', {
    url: supabaseUrl ? 'Present' : 'Missing',
    serviceKey: supabaseServiceKey ? `Present (${supabaseServiceKey.substring(0, 10)}...)` : 'Missing'
  })

  if (!supabaseUrl) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL is required for admin operations')
  }

  if (!supabaseServiceKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations')
  }

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}
