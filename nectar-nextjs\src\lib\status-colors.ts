/**
 * Centralized status color system for appointment statuses
 * Ensures consistency across all components and views
 */

export type AppointmentStatus = 
  | 'scheduled' 
  | 'confirmed' 
  | 'in_progress' 
  | 'completed' 
  | 'cancelled' 
  | 'no_show'

export interface StatusColorConfig {
  background: string
  border: string
  text: string
  badge: 'default' | 'secondary' | 'destructive' | 'outline'
  cardBackground: string
  cardBorder: string
  cardHover: string
}

/**
 * Unified status color configuration
 * Used across calendar, cards, badges, and all status displays
 */
export const STATUS_COLORS: Record<AppointmentStatus, StatusColorConfig> = {
  scheduled: {
    background: '#3b82f6', // blue-500
    border: '#3b82f6',
    text: '#ffffff',
    badge: 'outline',
    cardBackground: 'bg-blue-50',
    cardBorder: 'border-blue-200',
    cardHover: 'hover:bg-blue-100'
  },
  confirmed: {
    background: '#10b981', // emerald-500
    border: '#10b981',
    text: '#ffffff',
    badge: 'default',
    cardBackground: 'bg-emerald-50',
    cardBorder: 'border-emerald-200',
    cardHover: 'hover:bg-emerald-100'
  },
  in_progress: {
    background: '#f59e0b', // amber-500
    border: '#f59e0b',
    text: '#ffffff',
    badge: 'default',
    cardBackground: 'bg-amber-50',
    cardBorder: 'border-amber-200',
    cardHover: 'hover:bg-amber-100'
  },
  completed: {
    background: '#6b7280', // gray-500
    border: '#6b7280',
    text: '#ffffff',
    badge: 'secondary',
    cardBackground: 'bg-gray-50',
    cardBorder: 'border-gray-200',
    cardHover: 'hover:bg-gray-100'
  },
  cancelled: {
    background: '#ef4444', // red-500
    border: '#ef4444',
    text: '#ffffff',
    badge: 'destructive',
    cardBackground: 'bg-red-50',
    cardBorder: 'border-red-200',
    cardHover: 'hover:bg-red-100'
  },
  no_show: {
    background: '#8b5cf6', // violet-500
    border: '#8b5cf6',
    text: '#ffffff',
    badge: 'outline',
    cardBackground: 'bg-violet-50',
    cardBorder: 'border-violet-200',
    cardHover: 'hover:bg-violet-100'
  }
}

/**
 * Get status color for FullCalendar events
 */
export function getCalendarStatusColor(status: AppointmentStatus): string {
  return STATUS_COLORS[status]?.background || STATUS_COLORS.scheduled.background
}

/**
 * Get badge variant for status
 */
export function getStatusBadgeVariant(status: AppointmentStatus): 'default' | 'secondary' | 'destructive' | 'outline' {
  return STATUS_COLORS[status]?.badge || 'outline'
}

/**
 * Get card styling classes for status
 */
export function getStatusCardClasses(status: AppointmentStatus): string {
  const config = STATUS_COLORS[status] || STATUS_COLORS.scheduled
  return `${config.cardBackground} ${config.cardBorder} ${config.cardHover}`
}

/**
 * Get status text in Portuguese
 */
export function getStatusTextBR(status: AppointmentStatus): string {
  const statusMap: Record<AppointmentStatus, string> = {
    'scheduled': 'Agendado',
    'confirmed': 'Confirmado',
    'in_progress': 'Em Andamento',
    'completed': 'Concluído',
    'cancelled': 'Cancelado',
    'no_show': 'Faltou'
  }
  
  return statusMap[status] || status
}

/**
 * Get all available statuses for dropdowns/selects
 */
export function getAllStatuses(): Array<{ value: AppointmentStatus; label: string; color: string }> {
  return Object.entries(STATUS_COLORS).map(([status, config]) => ({
    value: status as AppointmentStatus,
    label: getStatusTextBR(status as AppointmentStatus),
    color: config.background
  }))
}

/**
 * Check if status allows certain actions
 */
export function canStartConsultation(status: AppointmentStatus): boolean {
  return ['scheduled', 'confirmed'].includes(status)
}

export function canCompleteConsultation(status: AppointmentStatus): boolean {
  return status === 'in_progress'
}

export function canEditAppointment(status: AppointmentStatus): boolean {
  return !['completed', 'cancelled'].includes(status)
}

export function canCancelAppointment(status: AppointmentStatus): boolean {
  return !['completed', 'cancelled'].includes(status)
}

/**
 * Get status priority for sorting (lower number = higher priority)
 */
export function getStatusPriority(status: AppointmentStatus): number {
  const priorities: Record<AppointmentStatus, number> = {
    'in_progress': 1,
    'confirmed': 2,
    'scheduled': 3,
    'completed': 4,
    'no_show': 5,
    'cancelled': 6
  }
  
  return priorities[status] || 999
}

/**
 * Get next logical status for workflow
 */
export function getNextStatus(currentStatus: AppointmentStatus): AppointmentStatus | null {
  const workflow: Record<AppointmentStatus, AppointmentStatus | null> = {
    'scheduled': 'confirmed',
    'confirmed': 'in_progress',
    'in_progress': 'completed',
    'completed': null,
    'cancelled': null,
    'no_show': null
  }
  
  return workflow[currentStatus] || null
}

/**
 * Check if status transition is valid
 */
export function isValidStatusTransition(from: AppointmentStatus, to: AppointmentStatus): boolean {
  const validTransitions: Record<AppointmentStatus, AppointmentStatus[]> = {
    'scheduled': ['confirmed', 'cancelled', 'no_show'],
    'confirmed': ['in_progress', 'cancelled', 'no_show'],
    'in_progress': ['completed', 'cancelled'],
    'completed': [], // Final state
    'cancelled': [], // Final state
    'no_show': [] // Final state
  }
  
  return validTransitions[from]?.includes(to) || false
}

/**
 * Get status icon name (for lucide-react icons)
 */
export function getStatusIcon(status: AppointmentStatus): string {
  const icons: Record<AppointmentStatus, string> = {
    'scheduled': 'Calendar',
    'confirmed': 'CheckCircle',
    'in_progress': 'Clock',
    'completed': 'CheckCircle2',
    'cancelled': 'XCircle',
    'no_show': 'AlertCircle'
  }
  
  return icons[status] || 'Calendar'
}
