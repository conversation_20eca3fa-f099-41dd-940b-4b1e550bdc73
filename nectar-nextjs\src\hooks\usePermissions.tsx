'use client'

import { useState, useEffect, createContext, useContext, useMemo, useCallback } from 'react'
import { makeAuthenticatedRequest } from '@/lib/api-client'
import { debugLog } from '@/lib/debug-utils'

interface UserData {
  id: string
  email: string
  name: string
  role: string
}

interface PermissionsContextType {
  userRole: string | null
  loading: boolean
  isAdmin: boolean
  isHealthcareProfessional: boolean
  isSecretary: boolean
  hasRole: (role: string) => boolean
  canStartConsultation: boolean
  canViewMedicalRecords: boolean
  canManageUsers: boolean
  canViewAllData: boolean
  accessibleHealthcareProfessionals: string[]
  refreshAccessibleProfessionals: () => void
  refetch: () => void
}

const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined)

export function PermissionsProvider({ children }: { children: React.ReactNode }) {
  const [userRole, setUserRole] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [accessibleHealthcareProfessionals, setAccessibleHealthcareProfessionals] = useState<string[]>([])

  const fetchUserRole = async () => {
    try {
      setLoading(true)
      debugLog.info('🚀 Starting user role fetch...')
      
      // Fetch current user profile which includes role
      const response = await makeAuthenticatedRequest('/api/profile')
      if (response.ok) {
        const userData = await response.json()
        const role = userData.data?.role || userData.role || null
        setUserRole(role)
        debugLog.info('🔄 Fetched user role:', role)
      } else {
        debugLog.error('❌ Failed to fetch user profile:', response.status, response.statusText)
        setUserRole(null)
      }
    } catch (error) {
      debugLog.critical('💥 Error fetching user role:', error)
      setUserRole(null)
    } finally {
      setLoading(false)
      debugLog.info('✅ User role fetch completed')
    }
  }

  const fetchAccessibleHealthcareProfessionals = useCallback(async () => {
    try {
      // Only fetch for secretaries who need association-based access
      if (userRole !== 'secretary') {
        setAccessibleHealthcareProfessionals([])
        return
      }

      const response = await makeAuthenticatedRequest('/api/healthcare-professionals')
      if (response.ok) {
        const data = await response.json()
        const professionals = data.data || data || []
        const professionalIds = professionals.map((p: any) => p.id)
        setAccessibleHealthcareProfessionals(professionalIds)
        debugLog.info('🏥 Accessible healthcare professionals:', professionalIds)
      }
    } catch (error) {
      debugLog.critical('💥 Error fetching accessible healthcare professionals:', error)
      setAccessibleHealthcareProfessionals([])
    }
  }, [userRole])

  useEffect(() => {
    fetchUserRole()
  }, [])

  useEffect(() => {
    if (userRole) {
      fetchAccessibleHealthcareProfessionals()
    }
  }, [userRole, fetchAccessibleHealthcareProfessionals])

  const hasRole = useCallback((role: string) => {
    return userRole === role
  }, [userRole])
  
  // Memoize role checks to prevent unnecessary re-calculations
  const roleChecks = useMemo(() => ({
    isAdmin: userRole === 'admin',
    isHealthcareProfessional: userRole === 'healthcare_professional',
    isSecretary: userRole === 'secretary'
  }), [userRole])
  
  // Memoize permission checks
  const permissionChecks = useMemo(() => ({
    canStartConsultation: roleChecks.isAdmin || roleChecks.isHealthcareProfessional,
    canViewMedicalRecords: roleChecks.isAdmin || roleChecks.isHealthcareProfessional,
    canManageUsers: roleChecks.isAdmin,
    canViewAllData: roleChecks.isAdmin
  }), [roleChecks])

  // Only log on actual changes, not on every render
  useEffect(() => {
    if (userRole !== null) {
      debugLog.info('👤 Role checks:', roleChecks)
      debugLog.info('🔐 Permission checks:', permissionChecks)
    }
  }, [userRole, roleChecks, permissionChecks])

  const value = useMemo(() => ({
    userRole,
    loading,
    ...roleChecks,
    hasRole,
    ...permissionChecks,
    accessibleHealthcareProfessionals,
    refreshAccessibleProfessionals: fetchAccessibleHealthcareProfessionals,
    refetch: fetchUserRole
  }), [
    userRole,
    loading,
    roleChecks,
    hasRole,
    permissionChecks,
    accessibleHealthcareProfessionals,
    fetchAccessibleHealthcareProfessionals
  ])

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  )
}

export function usePermissions() {
  const context = useContext(PermissionsContext)
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider')
  }
  return context
}

// Role gate component - simplified
interface RoleGateProps {
  roles: string[]
  children: React.ReactNode
  fallback?: React.ReactNode
  requireAll?: boolean
}

export function RoleGate({ roles, children, fallback = null, requireAll = false }: RoleGateProps) {
  const { hasRole, loading } = usePermissions()

  if (loading) {
    return <div>Loading...</div>
  }

  const hasRequiredRoles = requireAll 
    ? roles.every(role => hasRole(role))
    : roles.some(role => hasRole(role))

  if (!hasRequiredRoles) {
    return <>{fallback}</>
  }

  return <>{children}</>
}