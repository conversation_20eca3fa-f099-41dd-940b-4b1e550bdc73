import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import { formatTimeBR, getAppointmentTypeBR } from '@/lib/date-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Get today's date range
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      // Fetch today's appointments count
      const { count: todayCount } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('start_time', today.toISOString())
        .lt('start_time', tomorrow.toISOString())

      // Fetch total patients count
      const { count: patientsCount } = await supabase
        .from('patients')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)

      // Fetch recent appointments with patient info
      const { data: appointments } = await supabase
        .from('appointments')
        .select(`
          *,
          patients!inner(name)
        `)
        .eq('user_id', userId)
        .gte('start_time', today.toISOString())
        .order('start_time')
        .limit(5)

      const stats = {
        todayAppointments: todayCount || 0,
        totalPatients: patientsCount || 0,
        unreadMessages: 5, // Mock data for now
        monthlyRevenue: 8250 // Mock data for now
      }

      const recentAppointments = appointments?.map((apt: any) => ({
        time: formatTimeBR(apt.start_time),
        patient: apt.patients.name,
        type: getAppointmentTypeBR(apt.type),
        status: apt.status
      })) || []

      return createApiResponse({
        stats,
        recentAppointments
      })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
