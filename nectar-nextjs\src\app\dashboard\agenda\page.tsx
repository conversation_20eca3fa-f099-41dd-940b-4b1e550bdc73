"use client"

import React, { useState, useEffect } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar as CalendarIcon, Clock, Plus, Users, Grid3X3, X, Play, FileText, CheckCircle } from 'lucide-react';
import ConfirmDialog from '@/components/ConfirmDialog';
import TimeSlotCard from '@/components/TimeSlotCard';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatDateBR, formatTimeBR, formatDateTimeBR, getAppointmentStatusBR, getAppointmentTypeBR, toLocalISOString } from '@/lib/date-utils';
import { makeAuthenticatedRequest } from '@/lib/api-client';
import { getStatusBadgeVariant, getStatusCardClasses, getStatusTextBR, type AppointmentStatus } from '@/lib/status-colors';
import { generateTimeSlots, type TimeSlot } from '@/lib/time-slots';
import FullCalendarView from '@/components/FullCalendarView';
import AppointmentForm from '@/components/AppointmentForm';
import type { DateSelectArg } from '@fullcalendar/core';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/hooks/usePermissions';
import { useSelectedProfessional } from '@/contexts/SelectedProfessionalContext';
import { debugLog } from '@/lib/debug-utils';

type Patient = {
  id: string;
  name: string;
  email?: string;
  phone?: string;
};

type HealthcareProfessional = {
  id: string;
  name: string;
  specialty: string | null;
  is_active: boolean;
  user_id: string;
};

type Appointment = {
  id: string;
  title: string;
  description: string | null;
  patient_id: string;
  patient_name?: string;
  healthcare_professional_id: string | null;
  healthcare_professional_name?: string;
  start_time: string;
  end_time: string;
  type: string;
  status: string;
  total_price: number | null;
};

type WorkingDay = {
  enabled: boolean;
  working_hours_start: string;
  working_hours_end: string;
  break_intervals: { start: string; end: string; }[];
};

type ClinicSettings = {
  working_hours_start?: string;
  working_hours_end?: string;
  working_days?: number[];
  appointment_duration_minutes: number;
  allow_weekend_appointments?: boolean;
  clinic_name?: string;
  timezone?: string;
  weekly_schedule?: {
    monday: WorkingDay;
    tuesday: WorkingDay;
    wednesday: WorkingDay;
    thursday: WorkingDay;
    friday: WorkingDay;
    saturday: WorkingDay;
    sunday: WorkingDay;
  };
};

const AgendaPage = () => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [allAppointments, setAllAppointments] = useState<Appointment[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [healthcareProfessionals, setHealthcareProfessionals] = useState<HealthcareProfessional[]>([]);
  const [clinicSettings, setClinicSettings] = useState<ClinicSettings | null>(null);
  const [appointmentBlocks, setAppointmentBlocks] = useState<any[]>([]);
  const [allAppointmentBlocks, setAllAppointmentBlocks] = useState<any[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [appointmentFormOpen, setAppointmentFormOpen] = useState(false);
  const [currentView, setCurrentView] = useState<'calendar' | 'fullcalendar'>('calendar');
  const [appointmentFormData, setAppointmentFormData] = useState<any>(null);

  // Use global selected professional state
  const { selectedProfessional, setSelectedProfessional } = useSelectedProfessional();

  // Confirmation dialog states
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    title: string;
    description: string;
    onConfirm: () => void;
    variant?: 'default' | 'destructive';
  }>({
    open: false,
    title: '',
    description: '',
    onConfirm: () => {},
    variant: 'default'
  });
  const { toast } = useToast();
  const router = useRouter();
  const { 
    canStartConsultation, 
    canViewMedicalRecords, 
    isAdmin, 
    isHealthcareProfessional, 
    isSecretary,
    accessibleHealthcareProfessionals,
    userRole 
  } = usePermissions();

  // Fetch initial data that doesn't depend on selected date
  useEffect(() => {
    fetchAllAppointments();
    fetchAllAppointmentBlocks();
    fetchPatients();
    // Don't fetch clinic settings here - we'll fetch them when professional is selected
  }, []);

  // Fetch healthcare professionals when permissions are ready
  useEffect(() => {
    if (userRole) {
      fetchHealthcareProfessionals();
    }
  }, [userRole, isSecretary, accessibleHealthcareProfessionals]);

  // Auto-select first healthcare professional for better performance
  useEffect(() => {
    if (healthcareProfessionals.length > 0 && selectedProfessional === 'current') {
      const firstProfessional = healthcareProfessionals.find(prof => prof.is_active);
      if (firstProfessional) {
        setSelectedProfessional(firstProfessional.id);
      }
    }
  }, [healthcareProfessionals, selectedProfessional, setSelectedProfessional]);

  // Fetch date-specific appointments when selected date or professional changes
  useEffect(() => {
    if (selectedProfessional !== 'current' && selectedProfessional) {
      fetchAppointments();
      fetchAppointmentBlocks();
      fetchClinicSettings(); // Fetch clinic settings for the selected professional
    }
  }, [selectedDate, selectedProfessional]);

  // Generate time slots when appointments, blocks, or clinic settings change
  useEffect(() => {
    if (clinicSettings && selectedDate) {
      const slots = generateTimeSlots(
        selectedDate,
        clinicSettings as any,
        appointments,
        appointmentBlocks
      );
      setTimeSlots(slots);
    }
  }, [appointments, appointmentBlocks, clinicSettings, selectedDate]);

  // Calculate available days for calendar highlighting
  const calculateAvailableDays = React.useMemo(() => {
    if (!clinicSettings || !selectedProfessional || selectedProfessional === 'current') {
      return { hasAvailability: [], appointmentCounts: {} };
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const hasAvailability: Date[] = [];
    const appointmentCounts: Record<string, number> = {};

    // Filter appointments and blocks for the selected professional
    const professionalAppointments = allAppointments.filter(apt => 
      apt.healthcare_professional_id === selectedProfessional
    );
    
    const professionalBlocks = allAppointmentBlocks.filter(block => 
      block.healthcare_professional_id === selectedProfessional
    );

    // Calculate for next 90 days
    for (let i = 0; i < 90; i++) {
      const checkDate = new Date(today);
      checkDate.setDate(checkDate.getDate() + i);
      
      // Skip past dates (except today)
      if (checkDate < today) continue;

      const dateStr = checkDate.toISOString().split('T')[0];
      
      // Count non-cancelled appointments for this date and professional
      const dayAppointments = professionalAppointments.filter(apt => {
        const aptDate = new Date(apt.start_time).toISOString().split('T')[0];
        return aptDate === dateStr && apt.status !== 'cancelled';
      });
      
      appointmentCounts[dateStr] = dayAppointments.length;

      // Check if this day has availability based on new clinic settings format
      const dayOfWeek = checkDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const dayMapping = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const dayKey = dayMapping[dayOfWeek];

      let dayHasAvailability = false;

      // Handle both new and legacy clinic settings formats
      if ('weekly_schedule' in clinicSettings && clinicSettings.weekly_schedule) {
        // New format with weekly_schedule
        const daySchedule = (clinicSettings.weekly_schedule as any)[dayKey];
        
        if (daySchedule && daySchedule.enabled) {
          // Calculate available time slots for this day
          const slots = generateTimeSlots(
            checkDate,
            clinicSettings as any,
            dayAppointments,
            professionalBlocks.filter(block => {
              const blockDate = new Date(block.start_time).toISOString().split('T')[0];
              return blockDate === dateStr;
            })
          );
          
          // Check if there are any available slots
          const availableSlots = slots.filter(slot => slot.type === 'available');
          dayHasAvailability = availableSlots.length > 0;
        }
      } else {
        // Legacy format for backward compatibility
        const legacySettings = clinicSettings as any;
        if (legacySettings.working_days && legacySettings.working_days.includes(dayOfWeek)) {
          // Calculate available time slots for this day using legacy format
          const slots = generateTimeSlots(
            checkDate,
            clinicSettings as any,
            dayAppointments,
            professionalBlocks.filter(block => {
              const blockDate = new Date(block.start_time).toISOString().split('T')[0];
              return blockDate === dateStr;
            })
          );
          
          // Check if there are any available slots
          const availableSlots = slots.filter(slot => slot.type === 'available');
          dayHasAvailability = availableSlots.length > 0;
        }
      }

      if (dayHasAvailability) {
        hasAvailability.push(checkDate);
      }
    }

    debugLog.info('🗓️ Calendar availability calculation:', {
      totalDaysChecked: 90,
      daysWithAvailability: hasAvailability.length,
      hasAvailabilityDates: hasAvailability.map(d => d.toISOString().split('T')[0]),
      appointmentCounts,
      selectedProfessional,
      clinicSettingsType: 'weekly_schedule' in clinicSettings ? 'new' : 'legacy'
    });

    return { hasAvailability, appointmentCounts };
  }, [clinicSettings, allAppointments, allAppointmentBlocks, selectedProfessional]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);
      const dateStr = format(selectedDate, 'yyyy-MM-dd');
      let url = `/api/appointments?date=${dateStr}`;
      
      // Add healthcare professional filter if specific one is selected
      if (selectedProfessional && selectedProfessional !== 'current') {
        url += `&healthcare_professional_id=${selectedProfessional}`;
      }
      
      const response = await makeAuthenticatedRequest(url);
      if (!response.ok) throw new Error('Failed to fetch appointments');
      const result = await response.json();
      const data = result.data || result;
      setAppointments(Array.isArray(data) ? data : []);
    } catch (error) {
      debugLog.error('Error fetching appointments:', error);
      setAppointments([]);
      toast({
        title: "Erro ao carregar consultas",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchPatients = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/patients');
      if (!response.ok) throw new Error('Failed to fetch patients');
      const result = await response.json();
      debugLog.info('Patients API response:', result);
      const data = result.data || result;
      setPatients(Array.isArray(data) ? data : []);
    } catch (error) {
      debugLog.error('Error fetching patients:', error);
      setPatients([]);
      toast({
        title: "Erro ao carregar pacientes",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    }
  };

  const fetchHealthcareProfessionals = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/healthcare-professionals');
      if (!response.ok) throw new Error('Failed to fetch healthcare professionals');
      const result = await response.json();
      const data = result.data || result;
      
      // For secretaries, filter only accessible healthcare professionals
      let filteredProfessionals = Array.isArray(data) ? data : [];
      
      if (isSecretary && accessibleHealthcareProfessionals && accessibleHealthcareProfessionals.length > 0) {
        filteredProfessionals = filteredProfessionals.filter(prof => 
          accessibleHealthcareProfessionals.includes(prof.id)
        );
        debugLog.info('🔒 Filtered healthcare professionals for secretary:', filteredProfessionals.length);
      }
      
      setHealthcareProfessionals(filteredProfessionals);
    } catch (error) {
      debugLog.error('Error fetching healthcare professionals:', error);
      setHealthcareProfessionals([]);
    }
  };

  const fetchAllAppointments = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/appointments');
      if (!response.ok) throw new Error('Failed to fetch all appointments');
      const result = await response.json();
      const data = result.data || result;
      setAllAppointments(Array.isArray(data) ? data : []);
    } catch (error) {
      debugLog.error('Error fetching all appointments:', error);
      setAllAppointments([]);
    }
  };

  const fetchAllAppointmentBlocks = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/appointment-blocks');
      if (!response.ok) {
        if (response.status === 404) {
          setAllAppointmentBlocks([]);
          return;
        }
        throw new Error('Failed to fetch all appointment blocks');
      }
      const result = await response.json();
      const data = result.data || result;
      setAllAppointmentBlocks(Array.isArray(data) ? data : []);
    } catch (error) {
      debugLog.error('Error fetching all appointment blocks:', error);
      setAllAppointmentBlocks([]);
    }
  };

  const fetchClinicSettings = async () => {
    try {
      let url = '/api/clinic-settings';
      
      // Add healthcare professional filter if specific one is selected
      if (selectedProfessional && selectedProfessional !== 'current') {
        url += `?healthcare_professional_id=${selectedProfessional}`;
      }
      
      const response = await makeAuthenticatedRequest(url);
      if (!response.ok) throw new Error('Failed to fetch clinic settings');
      const result = await response.json();
      const data = result.data || result;
      setClinicSettings(data);
      
      debugLog.info('✅ Clinic settings loaded for professional:', {
        selectedProfessional,
        appointmentDuration: data.appointment_duration_minutes,
        hasWeeklySchedule: !!data.weekly_schedule
      });
    } catch (error) {
      debugLog.error('Error fetching clinic settings:', error);
      // Set default settings if fetch fails
      setClinicSettings({
        working_hours_start: '08:00',
        working_hours_end: '18:00',
        working_days: [1, 2, 3, 4, 5],
        appointment_duration_minutes: 30,
        allow_weekend_appointments: false
      });
    }
  };

  const fetchAppointmentBlocks = async () => {
    try {
      const dateStr = format(selectedDate, 'yyyy-MM-dd');
      let url = `/api/appointment-blocks?date=${dateStr}`;
      
      // Add healthcare professional filter if specific one is selected
      if (selectedProfessional && selectedProfessional !== 'current') {
        url += `&healthcare_professional_id=${selectedProfessional}`;
      }
      
      const response = await makeAuthenticatedRequest(url);
      if (!response.ok) {
        // If the API doesn't exist yet, just set empty array
        if (response.status === 404) {
          setAppointmentBlocks([]);
          return;
        }
        throw new Error('Failed to fetch appointment blocks');
      }
      const result = await response.json();
      const data = result.data || result;
      setAppointmentBlocks(Array.isArray(data) ? data : []);
    } catch (error) {
      debugLog.error('Error fetching appointment blocks:', error);
      setAppointmentBlocks([]);
    }
  };

  const handleAppointmentCreate = (selectInfo?: DateSelectArg) => {
    const initialData: any = {};

    if (selectInfo) {
      // FullCalendar provides dates in local timezone, use them directly
      initialData.start_time = toLocalISOString(selectInfo.start);
      initialData.end_time = toLocalISOString(selectInfo.end);
    } else if (selectedDate) {
      // Create appointment for selected date at 9:00 AM
      const startTime = new Date(selectedDate);
      startTime.setHours(9, 0, 0, 0);
      const endTime = new Date(startTime);
      endTime.setMinutes(endTime.getMinutes() + 30);

      initialData.start_time = toLocalISOString(startTime);
      initialData.end_time = toLocalISOString(endTime);
    }

    // Auto-assign to selected healthcare professional if specific one is selected
    if (selectedProfessional && selectedProfessional !== 'current') {
      initialData.healthcare_professional_id = selectedProfessional;
    }

    setAppointmentFormData(initialData);
    setAppointmentFormOpen(true);
  };

  const handleSlotClick = (slot: TimeSlot) => {
    if (slot.type === 'available') {
      // Open appointment form with the slot time pre-filled
      const initialData: any = {
        start_time: toLocalISOString(slot.start_time),
        end_time: toLocalISOString(slot.end_time),
      };
      
      // Auto-assign to selected healthcare professional if specific one is selected
      if (selectedProfessional && selectedProfessional !== 'current') {
        initialData.healthcare_professional_id = selectedProfessional;
      }
      
      setAppointmentFormData(initialData);
      setAppointmentFormOpen(true);
    } else if (slot.type === 'appointment' && slot.appointment) {
      // Handle existing appointment click
      handleAppointmentClick(slot.appointment);
    }
  };

  const handleBlockSlot = async (slot: TimeSlot, event: React.MouseEvent) => {
    event.stopPropagation();
    
    setConfirmDialog({
      open: true,
      title: 'Bloquear Horário',
      description: `Deseja bloquear o horário de ${formatTimeBR(slot.start_time.toISOString())} até ${formatTimeBR(slot.end_time.toISOString())}? Este horário ficará indisponível para agendamentos.`,
      variant: 'destructive',
      onConfirm: () => performBlockSlot(slot)
    });
  };

  const handleUnblockSlot = async (slot: TimeSlot, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (!slot.block || !slot.block.id) {
      toast({
        title: "Erro",
        description: "Não foi possível identificar o bloqueio para remover.",
        variant: "destructive"
      });
      return;
    }
    
    setConfirmDialog({
      open: true,
      title: 'Desbloquear Horário',
      description: `Deseja desbloquear o horário de ${formatTimeBR(slot.start_time.toISOString())} até ${formatTimeBR(slot.end_time.toISOString())}? Este horário ficará disponível para agendamentos.`,
      variant: 'default',
      onConfirm: () => performUnblockSlot(slot)
    });
  };

  const performBlockSlot = async (slot: TimeSlot) => {
    try {
      const blockData: any = {
        start_time: slot.start_time.toISOString(),
        end_time: slot.end_time.toISOString(),
        reason: 'Horário bloqueado pelo médico'
      };

      // Add healthcare professional if one is selected
      if (selectedProfessional && selectedProfessional !== 'current') {
        blockData.healthcare_professional_id = selectedProfessional;
      }

      const response = await makeAuthenticatedRequest('/api/appointment-blocks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(blockData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        
        if (response.status === 409) {
          // Conflict - slot already occupied
          toast({
            title: "Horário Ocupado",
            description: errorData?.message || "Este horário já está ocupado ou bloqueado.",
            variant: "destructive"
          });
          return;
        }

        if (response.status === 404) {
          toast({
            title: "Funcionalidade em desenvolvimento",
            description: "A funcionalidade de bloqueio de horários ainda está sendo implementada.",
            variant: "default"
          });
          return;
        }

        throw new Error(errorData?.message || 'Failed to block time slot');
      }

      const result = await response.json();
      
      toast({
        title: "Sucesso!",
        description: result.message || "Horário bloqueado com sucesso.",
      });

      // Refresh data
      fetchAppointmentBlocks();
      fetchAllAppointmentBlocks();
      fetchAppointments();
    } catch (error) {
      debugLog.error('Error blocking time slot:', error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao bloquear horário.",
        variant: "destructive"
      });
    }
  };

  const performUnblockSlot = async (slot: TimeSlot) => {
    try {
      if (!slot.block || !slot.block.id) {
        throw new Error('ID do bloqueio não encontrado');
      }

      const response = await makeAuthenticatedRequest(`/api/appointment-blocks/${slot.block.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || 'Failed to unblock time slot');
      }

      toast({
        title: "Sucesso!",
        description: "Horário desbloqueado com sucesso.",
      });

      // Refresh data
      fetchAppointmentBlocks();
      fetchAllAppointmentBlocks();
      fetchAppointments();
    } catch (error) {
      debugLog.error('Error unblocking time slot:', error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao desbloquear horário.",
        variant: "destructive"
      });
    }
  };

  const handleAppointmentClick = (appointment: Appointment) => {
    // For in-progress and completed appointments, navigate to medical record screen (only for healthcare professionals)
    if ((appointment.status === 'in_progress' || appointment.status === 'completed') && canViewMedicalRecords) {
      debugLog.info('Navigating to medical record for appointment:', appointment.id);
      router.push(`/dashboard/prontuario/${appointment.patient_id}?appointment_id=${appointment.id}`);
      return;
    }

    // For other statuses, open edit form with appointment data
    debugLog.info('Appointment clicked for editing:', appointment);

    const editData = {
      id: appointment.id,
      title: appointment.title,
      description: appointment.description,
      patient_id: appointment.patient_id,
      healthcare_professional_id: appointment.healthcare_professional_id,
      start_time: appointment.start_time,
      end_time: appointment.end_time,
      type: appointment.type,
      status: appointment.status,
      total_price: appointment.total_price,
    };

    setAppointmentFormData(editData);
    setAppointmentFormOpen(true);
  };

  const handleAppointmentUpdate = async (appointmentId: string, newStart: Date, newEnd: Date) => {
    try {
      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          start_time: newStart.toISOString(),
          end_time: newEnd.toISOString(),
        })
      });

      if (!response.ok) throw new Error('Failed to update appointment');

      // Refresh both daily and all appointments for calendar views
      fetchAppointments();
      fetchAllAppointments();
    } catch (error) {
      debugLog.error('Error updating appointment:', error);
      throw error;
    }
  };

  const handleAppointmentSubmit = async (data: any) => {
    try {
      const isEditing = data.id;
      const url = isEditing ? `/api/appointments/${data.id}` : '/api/appointments';
      const method = isEditing ? 'PUT' : 'POST';

      // Remove id from data for API call
      const { id, ...submitData } = data;

      const response = await makeAuthenticatedRequest(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(submitData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        
        if (response.status === 409) {
          // Conflict error - show specific message
          throw new Error(errorData?.message || 'Este horário já está ocupado. Escolha outro horário.');
        }
        
        throw new Error(errorData?.message || `Failed to ${isEditing ? 'update' : 'create'} appointment`);
      }

      const result = await response.json();
      
      // Show success message
      toast({
        title: "Sucesso!",
        description: result.message || `Consulta ${isEditing ? 'atualizada' : 'agendada'} com sucesso.`,
      });

      // Close the form
      setAppointmentFormOpen(false);
      setAppointmentFormData(null);

      // Refresh both daily and all appointments for calendar views
      fetchAppointments();
      fetchAllAppointments();
      fetchAppointmentBlocks(); // Also refresh blocks
      fetchAllAppointmentBlocks();
    } catch (error) {
      debugLog.error(`Error ${data.id ? 'updating' : 'creating'} appointment:`, error);
      
      // Show error toast
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao processar consulta.",
        variant: "destructive"
      });
      
      throw error;
    }
  };

  // Calculate appointment counts for calendar indicators
  const appointmentCounts = React.useMemo(() => {
    const counts: Record<string, number> = {};
    appointments.forEach(appointment => {
      const date = new Date(appointment.start_time).toISOString().split('T')[0];
      counts[date] = (counts[date] || 0) + 1;
    });
    return counts;
  }, [appointments]);

  const updateAppointmentStatus = async (appointmentId: string, status: string) => {
    try {
      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });

      if (!response.ok) throw new Error('Failed to update appointment status');

      toast({
        title: "Sucesso!",
        description: "Status da consulta atualizado.",
      });

      fetchAppointments();
      fetchAllAppointments();
    } catch (error) {
      debugLog.error('Error updating appointment status:', error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar status da consulta.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteAppointment = (appointmentId: string) => {
    setConfirmDialog({
      open: true,
      title: 'Excluir Consulta',
      description: 'Tem certeza que deseja excluir esta consulta? Esta ação não pode ser desfeita.',
      variant: 'destructive',
      onConfirm: () => performDeleteAppointment(appointmentId)
    });
  };

  const performDeleteAppointment = async (appointmentId: string) => {

    try {
      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete appointment');

      toast({
        title: "Sucesso!",
        description: "Consulta excluída com sucesso.",
      });

      fetchAppointments();
      fetchAllAppointments();
    } catch (error) {
      debugLog.error('Error deleting appointment:', error);
      toast({
        title: "Erro",
        description: "Erro ao excluir consulta.",
        variant: "destructive"
      });
    }
  };

  const handleCancelAppointment = async (appointment: Appointment, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the edit click handler

    setConfirmDialog({
      open: true,
      title: 'Cancelar Consulta',
      description: `Tem certeza que deseja cancelar a consulta de ${appointment.patient_name}?`,
      variant: 'destructive',
      onConfirm: () => performCancelAppointment(appointment.id)
    });
  };

  const performCancelAppointment = async (appointmentId: string) => {
    try {
      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'cancelled' })
      });

      if (!response.ok) throw new Error('Failed to cancel appointment');

      toast({
        title: "Sucesso!",
        description: "Consulta cancelada com sucesso.",
      });

      fetchAppointments();
      fetchAllAppointments();
    } catch (error) {
      debugLog.error('Error canceling appointment:', error);
      toast({
        title: "Erro",
        description: "Erro ao cancelar consulta.",
        variant: "destructive"
      });
    }
  };

  const handleStartConsultation = async (appointment: Appointment, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the edit click handler

    setConfirmDialog({
      open: true,
      title: 'Iniciar Atendimento',
      description: `Deseja iniciar o atendimento para ${appointment.patient_name}?`,
      variant: 'default',
      onConfirm: () => performStartConsultation(appointment)
    });
  };

  const performStartConsultation = async (appointment: Appointment) => {

    try {
      // Update appointment status to in_progress
      const response = await makeAuthenticatedRequest(`/api/appointments/${appointment.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'in_progress' })
      });

      if (!response.ok) throw new Error('Failed to start consultation');

      toast({
        title: "Sucesso!",
        description: "Atendimento iniciado com sucesso.",
      });

      // Navigate to medical record screen
      router.push(`/dashboard/prontuario/${appointment.patient_id}?appointment_id=${appointment.id}`);
    } catch (error) {
      debugLog.error('Error starting consultation:', error);
      toast({
        title: "Erro",
        description: "Erro ao iniciar atendimento.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Agenda</h1>
          <p className="text-muted-foreground">Gerencie suas consultas e horários</p>
        </div>

        <div className="flex items-center gap-2">
          <Button onClick={() => handleAppointmentCreate()}>
            <Plus className="mr-2 h-4 w-4" />
            Nova Consulta
          </Button>
        </div>
      </div>

      <Tabs
        value={currentView}
        onValueChange={(value) => {
          // Prevent tab switching during loading to avoid race conditions
          if (loading) {
            toast({
              title: "Aguarde",
              description: "Aguarde o carregamento dos dados antes de trocar de aba.",
              variant: "default",
            });
            return;
          }
          setCurrentView(value as 'calendar' | 'fullcalendar');
        }}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger
            value="calendar"
            className={`flex items-center gap-2 text-xs sm:text-sm ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={loading}
          >
            <CalendarIcon className="h-4 w-4" />
            <span className="hidden xs:inline">Calendário</span>
            <span className="xs:hidden">Cal.</span>
            {loading && <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1"></div>}
          </TabsTrigger>
          <TabsTrigger
            value="fullcalendar"
            className={`flex items-center gap-2 text-xs sm:text-sm ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={loading}
          >
            <Grid3X3 className="h-4 w-4" />
            <span className="hidden xs:inline">Agenda Completa</span>
            <span className="xs:hidden">Agenda</span>
            {loading && <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1"></div>}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <CalendarIcon className="mr-2 h-5 w-5 text-primary" />
                  Calendário
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2 sm:p-6 pt-0">
                <div className="flex justify-center">
                  <Calendar
                    mode="single"
                    locale={ptBR}
                    selected={selectedDate}
                    onSelect={(date) => date && setSelectedDate(date)}
                    modifiers={{
                      hasAvailability: calculateAvailableDays.hasAvailability,
                      hasAppointments: Object.keys(calculateAvailableDays.appointmentCounts).map(dateStr => new Date(dateStr))
                    }}
                    modifiersClassNames={{
                      hasAvailability: "has-availability",
                      hasAppointments: "has-appointments"
                    }}
                    className="rounded-md border-0 shadow-none w-full [--cell-size:2rem] sm:[--cell-size:2.75rem] lg:[--cell-size:3rem]"
                  />
                </div>
              </CardContent>
            </Card>

            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center text-lg">
                      <Clock className="mr-2 h-5 w-5 text-primary" />
                      <span className="hidden sm:inline">Horários - {formatDateBR(selectedDate)}</span>
                      <span className="sm:hidden">Horários</span>
                    </CardTitle>
                    
                    {/* Healthcare Professional Filter */}
                    <div className="flex items-center gap-2">
                      <Select value={selectedProfessional} onValueChange={setSelectedProfessional}>
                        <SelectTrigger className="w-[180px] sm:w-[200px]">
                          <SelectValue placeholder="Selecionar médico" />
                        </SelectTrigger>
                        <SelectContent>
                          {healthcareProfessionals
                            .filter(prof => prof.is_active)
                            .map(professional => (
                              <SelectItem key={professional.id} value={professional.id}>
                                {professional.name}
                                {professional.specialty && ` - ${professional.specialty}`}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <CardDescription>
                    {timeSlots.filter(slot => slot.type === 'appointment' && slot.appointment?.status !== 'cancelled').length} consulta(s) agendada(s) • {timeSlots.filter(slot => slot.type === 'available').length} horário(s) disponível(is)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Clock className="mx-auto h-12 w-12 mb-4 opacity-50 animate-spin" />
                      <p>Carregando consultas...</p>
                    </div>
                  ) : timeSlots.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Users className="mx-auto h-12 w-12 mb-4 opacity-50" />
                      <p>Nenhum horário disponível para este dia</p>
                      <p className="text-xs mt-2">Verifique as configurações da clínica</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {/* Time slots grid */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        {timeSlots.map((slot) => (
                          <TimeSlotCard
                            key={slot.id}
                            slot={slot}
                            onSlotClick={handleSlotClick}
                            onBlockSlot={handleBlockSlot}
                            onUnblockSlot={handleUnblockSlot}
                            onConfirmAppointment={(appointment, event) => updateAppointmentStatus(appointment.id, 'confirmed')}
                            onStartConsultation={handleStartConsultation}
                            onCancelAppointment={handleCancelAppointment}
                            canBlockSlots={isAdmin || isHealthcareProfessional || isSecretary}
                            canStartConsultation={canStartConsultation}
                            className="min-h-[80px]"
                          />
                        ))}
                      </div>
                      
                      {/* Legend */}
                      <div className="mt-4 pt-4 border-t">
                        <div className="flex flex-wrap gap-4 text-xs">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 border-2 border-dashed border-green-300 bg-green-50 rounded"></div>
                            <span>Disponível</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 border border-blue-300 bg-blue-50 rounded"></div>
                            <span>Consulta</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 border border-red-300 bg-red-50 rounded"></div>
                            <span>Bloqueado</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 border border-orange-300 bg-orange-50 rounded"></div>
                            <span>Conflito</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 border-l-4 border-l-purple-500 border border-blue-300 bg-blue-50 rounded"></div>
                            <span>Encaixe</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="fullcalendar" className="space-y-6 mt-6">
          <FullCalendarView
            appointments={allAppointments}
            appointmentBlocks={allAppointmentBlocks}
            healthcareProfessionals={healthcareProfessionals}
            onAppointmentCreate={handleAppointmentCreate}
            onAppointmentClick={handleAppointmentClick}
            onAppointmentUpdate={handleAppointmentUpdate}
            loading={loading}
            selectedProfessional={selectedProfessional}
            onSelectedProfessionalChange={setSelectedProfessional}
            onUnblockSlot={(block) => handleUnblockSlot({ 
              id: block.id, 
              start_time: new Date(block.start_time), 
              end_time: new Date(block.end_time), 
              isAvailable: false,
              isBlocked: true,
              type: 'blocked',
              block: block 
            }, {} as React.MouseEvent)}
          />
        </TabsContent>
      </Tabs>

      <AppointmentForm
        open={appointmentFormOpen}
        onOpenChange={setAppointmentFormOpen}
        patients={patients}
        healthcareProfessionals={healthcareProfessionals.map(prof => ({
          ...prof,
          specialty: prof.specialty || undefined
        }))}
        initialData={appointmentFormData}
        onSubmit={handleAppointmentSubmit}
        loading={loading}
      />

      <ConfirmDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}
        title={confirmDialog.title}
        description={confirmDialog.description}
        variant={confirmDialog.variant}
        onConfirm={confirmDialog.onConfirm}
      />
    </div>
  );
};

export default AgendaPage;
