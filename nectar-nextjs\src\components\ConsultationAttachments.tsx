"use client"

import React, { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { 
  Upload, 
  File, 
  Image, 
  FileText, 
  Download, 
  Trash2, 
  Eye,
  AlertCircle,
  Paperclip
} from 'lucide-react'
import { makeAuthenticatedRequest } from '@/lib/api-client'
import { formatDateTimeBR } from '@/lib/date-utils'
import { ConsultationAttachmentsSkeleton } from '@/components/ui/skeleton-components'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'

interface ConsultationAttachment {
  id: string
  appointment_id: string
  file_name: string
  file_path: string
  file_size: number
  file_type: string
  uploaded_by_name: string
  created_at: string
}

interface ConsultationAttachmentsProps {
  appointmentId: string
  userDisplayName: string
  className?: string
}

export function ConsultationAttachments({ 
  appointmentId, 
  userDisplayName,
  className 
}: ConsultationAttachmentsProps) {
  const [attachments, setAttachments] = useState<ConsultationAttachment[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchAttachments()
  }, [appointmentId])

  const fetchAttachments = async () => {
    try {
      setLoading(true)
      const response = await makeAuthenticatedRequest(
        `/api/consultation-attachments?appointment_id=${appointmentId}`
      )
      
      if (!response.ok) throw new Error('Failed to fetch attachments')
      
      const result = await response.json()
      setAttachments(result.data || [])
    } catch (error) {
      console.error('Error fetching attachments:', error)
      toast({
        title: "Erro",
        description: "Erro ao carregar anexos da consulta.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file size (10MB)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      toast({
        title: "Arquivo muito grande",
        description: "O arquivo deve ter no máximo 10MB.",
        variant: "destructive"
      })
      return
    }

    // Validate file type
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'application/msword', 
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Tipo de arquivo não permitido",
        description: "Apenas imagens, PDFs e documentos de texto são permitidos.",
        variant: "destructive"
      })
      return
    }

    try {
      setUploading(true)
      
      const formData = new FormData()
      formData.append('file', file)
      formData.append('appointment_id', appointmentId)
      formData.append('uploaded_by_name', userDisplayName)

      const response = await makeAuthenticatedRequest('/api/consultation-attachments', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to upload file')
      }

      toast({
        title: "Sucesso",
        description: "Arquivo enviado com sucesso.",
      })

      // Refresh attachments list
      await fetchAttachments()
      
      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      toast({
        title: "Erro no upload",
        description: error instanceof Error ? error.message : "Erro ao enviar arquivo.",
        variant: "destructive"
      })
    } finally {
      setUploading(false)
    }
  }

  const handleDeleteAttachment = async (attachmentId: string) => {
    try {
      const response = await makeAuthenticatedRequest(
        `/api/consultation-attachments?id=${attachmentId}`,
        { method: 'DELETE' }
      )

      if (!response.ok) throw new Error('Failed to delete attachment')

      toast({
        title: "Sucesso",
        description: "Anexo removido com sucesso.",
      })

      // Refresh attachments list
      await fetchAttachments()
    } catch (error) {
      console.error('Error deleting attachment:', error)
      toast({
        title: "Erro",
        description: "Erro ao remover anexo.",
        variant: "destructive"
      })
    }
  }

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image className="h-8 w-8 text-blue-500" />
    } else if (fileType === 'application/pdf') {
      return <FileText className="h-8 w-8 text-red-500" />
    } else {
      return <File className="h-8 w-8 text-gray-500" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDownload = async (attachment: ConsultationAttachment) => {
    try {
      // Create a download URL for the file
      const response = await makeAuthenticatedRequest(
        `/api/consultation-attachments/download?path=${encodeURIComponent(attachment.file_path)}`
      )
      
      if (!response.ok) throw new Error('Failed to download file')
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = attachment.file_name
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Error downloading file:', error)
      toast({
        title: "Erro",
        description: "Erro ao baixar arquivo.",
        variant: "destructive"
      })
    }
  }

  if (loading) {
    return <ConsultationAttachmentsSkeleton />
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Paperclip className="h-5 w-5" />
              Anexos da Consulta
            </CardTitle>
            <CardDescription>
              {attachments.length} arquivo(s) anexado(s)
            </CardDescription>
          </div>
          
          <div>
            <input
              ref={fileInputRef}
              type="file"
              onChange={handleFileUpload}
              className="hidden"
              accept="image/*,.pdf,.doc,.docx,.txt"
            />
            <Button
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading}
              size="sm"
            >
              <Upload className="h-4 w-4 mr-2" />
              {uploading ? 'Enviando...' : 'Adicionar Arquivo'}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {attachments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Paperclip className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>Nenhum arquivo anexado a esta consulta</p>
            <p className="text-sm mt-2">
              Clique em "Adicionar Arquivo" para enviar documentos, imagens ou relatórios
            </p>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {attachments.map((attachment) => (
              <div key={attachment.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start space-x-3">
                  {getFileIcon(attachment.file_type)}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm truncate" title={attachment.file_name}>
                      {attachment.file_name}
                    </h4>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(attachment.file_size)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatDateTimeBR(attachment.created_at)}
                    </p>
                    <Badge variant="outline" className="text-xs mt-1">
                      {attachment.uploaded_by_name}
                    </Badge>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDownload(attachment)}
                    className="flex-1"
                  >
                    <Download className="h-3 w-3 mr-1" />
                    Baixar
                  </Button>
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button size="sm" variant="destructive">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Remover Anexo</AlertDialogTitle>
                        <AlertDialogDescription>
                          Tem certeza que deseja remover o arquivo "{attachment.file_name}"? 
                          Esta ação não pode ser desfeita.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDeleteAttachment(attachment.id)}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Remover
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
