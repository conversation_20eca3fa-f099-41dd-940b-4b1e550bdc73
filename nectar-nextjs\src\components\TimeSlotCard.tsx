import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Clock, Lock, Plus, User, Calendar, CheckCircle, Play, X, MessageCircle, History } from 'lucide-react';
import { formatSlotTime, type TimeSlot } from '@/lib/time-slots';
import { getStatusBadgeVariant, getStatusTextBR, type AppointmentStatus } from '@/lib/status-colors';
import { makeAuthenticatedRequest } from '@/lib/api-client';
import { useRouter } from 'next/navigation';

interface TimeSlotCardProps {
  slot: TimeSlot;
  onSlotClick: (slot: TimeSlot) => void;
  onBlockSlot: (slot: TimeSlot, event: React.MouseEvent) => void;
  onUnblockSlot?: (slot: TimeSlot, event: React.MouseEvent) => void;
  onConfirmAppointment?: (appointment: any, event: React.MouseEvent) => void;
  onStartConsultation?: (appointment: any, event: React.MouseEvent) => void;
  onCancelAppointment?: (appointment: any, event: React.MouseEvent) => void;
  canBlockSlots: boolean;
  canStartConsultation: boolean;
  className?: string;
}

const TimeSlotCard: React.FC<TimeSlotCardProps> = ({
  slot,
  onSlotClick,
  onBlockSlot,
  onUnblockSlot,
  onConfirmAppointment,
  onStartConsultation,
  onCancelAppointment,
  canBlockSlots,
  canStartConsultation,
  className = ''
}) => {
  const router = useRouter();
  const [patientMenuOpen, setPatientMenuOpen] = useState(false);
  
  // Debug: Log state changes
  React.useEffect(() => {
    console.log('Patient menu state changed:', patientMenuOpen);
  }, [patientMenuOpen]);

  const handleCardClick = () => {
    if (slot.isAvailable) {
      onSlotClick(slot);
    } else if (slot.appointment) {
      // Para consultas "em atendimento" ou "concluído", redirecionar para prontuário se possível
      if (['in_progress', 'completed'].includes(slot.appointment.status) && canStartConsultation) {
        // O redirecionamento será tratado no onSlotClick da página pai
        onSlotClick(slot);
      } else {
        // Para outros status, abrir formulário de edição
        onSlotClick(slot);
      }
    }
  };

  const handleBlockClick = (event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the card click
    onBlockSlot(slot, event);
  };

  const handleUnblockClick = (event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the card click
    if (onUnblockSlot) {
      onUnblockSlot(slot, event);
    }
  };

  const handleConfirmClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onConfirmAppointment && slot.appointment) {
      onConfirmAppointment(slot.appointment, event);
    }
  };

  const handleStartClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onStartConsultation && slot.appointment) {
      onStartConsultation(slot.appointment, event);
    }
  };

  const handleCancelClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onCancelAppointment && slot.appointment) {
      onCancelAppointment(slot.appointment, event);
    }
  };

  const handlePatientWhatsApp = async (event: React.MouseEvent) => {
    event.stopPropagation();
    setPatientMenuOpen(false);
    
    if (slot.appointment) {
      let phoneNumber = slot.appointment.patient_phone;
      
      // If phone is not available in appointment, try to fetch it from patient API
      if (!phoneNumber && slot.appointment.patient_id) {
        try {
          const response = await makeAuthenticatedRequest(`/api/patients/${slot.appointment.patient_id}`);
          
          if (response.ok) {
            const result = await response.json();
            const patientData = result.data || result;
            phoneNumber = patientData.phone;
          }
        } catch (error) {
          console.warn('Failed to fetch patient phone number:', error);
        }
      }
      
      if (phoneNumber) {
        // Remove all non-numeric characters from phone number
        const cleanPhone = phoneNumber.replace(/\D/g, '');
        
        // Ensure the phone number starts with country code (55 for Brazil)
        let formattedPhone = cleanPhone;
        if (!formattedPhone.startsWith('55')) {
          formattedPhone = '55' + formattedPhone;
        }
        
        const whatsappUrl = `https://wa.me/${formattedPhone}?text=Ol%C3%A1!`;
        window.open(whatsappUrl, '_blank');
      } else {
        // You might want to show a toast message here indicating that the phone wasn't available
        console.warn('Número de telefone do paciente não encontrado');
      }
    }
  };

  const handlePatientHistory = (event: React.MouseEvent) => {
    event.stopPropagation();
    setPatientMenuOpen(false);
    
    if (slot.appointment && slot.appointment.patient_id) {
      // Navigate to patient detail page with consultations tab
      router.push(`/dashboard/pacientes/${slot.appointment.patient_id}?tab=consultas`);
    }
  };

  const handlePatientNameClick = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation(); // Prevent triggering the card click
    console.log('Patient name clicked, current state:', patientMenuOpen);
    
    // Force the popover to open
    if (!patientMenuOpen) {
      setPatientMenuOpen(true);
      console.log('Opening patient menu');
    } else {
      setPatientMenuOpen(false);
      console.log('Closing patient menu');
    }
  };

  const getCardClasses = () => {
    const baseClasses = "transition-all duration-200 hover:shadow-md cursor-pointer relative overflow-visible";
    const subdivisionClasses = slot.isSubdivided ? "border-l-4" : "";
    
    // Check for conflicts
    const hasConflict = slot.appointment?.hasConflict;
    
    if (slot.type === 'available') {
      const availableClasses = slot.isSubdivided 
        ? "border-2 border-dashed border-green-300 hover:border-green-400 hover:bg-green-50 border-l-green-500"
        : "border-2 border-dashed border-green-300 hover:border-green-400 hover:bg-green-50";
      return `${baseClasses} ${availableClasses} ${subdivisionClasses} ${className}`;
    } else if (slot.type === 'appointment') {
      if (hasConflict) {
        return `${baseClasses} border-orange-300 bg-orange-50 hover:bg-orange-100 border-l-orange-500 ${subdivisionClasses} ${className}`;
      }
      const appointmentClasses = slot.isSubdivided
        ? "border-blue-300 bg-blue-50 hover:bg-blue-100 border-l-blue-500"
        : "border-blue-300 bg-blue-50 hover:bg-blue-100";
      return `${baseClasses} ${appointmentClasses} ${subdivisionClasses} ${className}`;
    } else if (slot.type === 'blocked') {
      const blockedClasses = slot.isSubdivided
        ? "border-red-300 bg-red-50 hover:bg-red-100 border-l-red-500"
        : "border-red-300 bg-red-50 hover:bg-red-100";
      return `${baseClasses} ${blockedClasses} ${subdivisionClasses} ${className}`;
    }
    
    return baseClasses;
  };

  const getSlotIcon = () => {
    if (slot.type === 'available') {
      return <Plus className="h-4 w-4 text-green-600" />;
    } else if (slot.type === 'appointment') {
      if (slot.appointment?.hasConflict) {
        return <User className="h-4 w-4 text-orange-600" />;
      }
      return <User className="h-4 w-4 text-blue-600" />;
    } else if (slot.type === 'blocked') {
      return <Lock className="h-4 w-4 text-red-600" />;
    }
    return <Clock className="h-4 w-4" />;
  };

  const getSlotTitle = () => {
    if (slot.type === 'available') {
      return slot.isSubdivided ? 'Disponível (Parcial)' : 'Horário Disponível';
    } else if (slot.type === 'appointment' && slot.appointment) {
      const title = slot.appointment.title || 'Consulta Agendada';
      const conflictSuffix = slot.appointment.hasConflict ? ' ⚠️ CONFLITO' : '';
      const timeInfo = slot.isSubdivided ? ` (${formatSlotTime(slot.start_time)}-${formatSlotTime(slot.end_time)})` : '';
      return `${title}${conflictSuffix}${timeInfo}`;
    } else if (slot.type === 'blocked' && slot.block) {
      return slot.block.reason || 'Horário Bloqueado';
    }
    return 'Slot';
  };

  const getSlotSubtitle = () => {
    if (slot.type === 'appointment' && slot.appointment) {
      const patientName = slot.appointment.patient_name || 'Paciente';
      
      // Return clickable patient name with popover
      return (
        <div onClick={(e) => e.stopPropagation()}>
          <Popover open={patientMenuOpen} onOpenChange={setPatientMenuOpen}>
            <PopoverTrigger asChild>
              <div
                className="text-left hover:underline focus:outline-none text-blue-600 hover:text-blue-800 font-medium transition-colors cursor-pointer inline-block"
                onClick={handlePatientNameClick}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handlePatientNameClick(e as any);
                  }
                }}
                title="Clique para ver opções do paciente"
              >
                {patientName} 📋
              </div>
            </PopoverTrigger>
            <PopoverContent 
              className="w-52 p-2" 
              align="start" 
              side="top" 
              sideOffset={4}
              avoidCollisions={true}
              collisionPadding={8}
            >
              <div className="space-y-2">
                <div className="text-sm font-semibold text-gray-700 px-2 py-1 border-b">
                  {patientName}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-left h-auto p-2 hover:bg-green-50"
                  onClick={handlePatientWhatsApp}
                >
                  <MessageCircle className="h-4 w-4 mr-2 text-green-600 sm:flex-shrink-0" />
                  <div className="text-left min-w-0 flex-1">
                    <div className="text-sm font-medium">WhatsApp</div>
                    <div className="text-xs text-muted-foreground sm:truncate">Enviar mensagem</div>
                  </div>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-left h-auto p-2 hover:bg-blue-50"
                  onClick={handlePatientHistory}
                >
                  <History className="h-4 w-4 mr-2 text-blue-600 sm:flex-shrink-0" />
                  <div className="text-left min-w-0 flex-1">
                    <div className="text-sm font-medium">Histórico</div>
                    <div className="text-xs text-muted-foreground sm:truncate">Ver consultas anteriores</div>
                  </div>
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      );
    } else if (slot.type === 'blocked') {
      return 'Indisponível';
    } else if (slot.type === 'available') {
      return slot.isSubdivided ? 'Clique para agendar neste período' : 'Clique para agendar';
    }
    return 'Clique para agendar';
  };

  const getAppointmentActions = () => {
    if (slot.type !== 'appointment' || !slot.appointment) {
      return null;
    }

    const appointment = slot.appointment;
    const status = appointment.status;

    const actions = [];

    // Botão "Confirmar" para consultas agendadas (todos os usuários)
    if (status === 'scheduled') {
      actions.push(
        <Button
          key="confirm"
          size="sm"
          variant="outline"
          className="h-6 px-2 text-xs text-green-600 hover:text-green-700 hover:bg-green-50"
          onClick={handleConfirmClick}
          title="Confirmar consulta"
        >
          <CheckCircle className="h-3 w-3 mr-1" />
          Confirmar
        </Button>
      );
    }

    // Botão "Iniciar" para consultas confirmadas (só médicos)
    if (status === 'confirmed' && canStartConsultation) {
      actions.push(
        <Button
          key="start"
          size="sm"
          variant="outline"
          className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50"
          onClick={handleStartClick}
          title="Iniciar atendimento"
        >
          <Play className="h-3 w-3 mr-1" />
          Iniciar
        </Button>
      );
    }

    // Botão "Cancelar" para consultas que ainda não foram iniciadas
    if (['scheduled', 'confirmed'].includes(status)) {
      actions.push(
        <Button
          key="cancel"
          size="sm"
          variant="outline"
          className="h-6 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
          onClick={handleCancelClick}
          title="Cancelar consulta"
        >
          <X className="h-3 w-3" />
        </Button>
      );
    }

    return actions.length > 0 ? (
      <>
        {actions}
      </>
    ) : null;
  };

  return (
    <Card className={getCardClasses()} onClick={handleCardClick}>
      <CardContent className="p-3">
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-start sm:justify-between sm:space-y-0">
          <div className="flex items-center space-x-2 flex-1 min-w-0">
            {getSlotIcon()}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">
                  {formatSlotTime(slot.start_time)} - {formatSlotTime(slot.end_time)}
                </span>
                {slot.isSubdivided && (
                  <Badge variant="outline" className="text-xs text-purple-600 border-purple-300">
                    Encaixe
                  </Badge>
                )}
                {slot.appointment?.hasConflict && (
                  <Badge variant="destructive" className="text-xs">
                    ⚠️ Conflito
                  </Badge>
                )}
                {slot.type === 'appointment' && slot.appointment && !slot.appointment.hasConflict && (
                  <Badge
                    variant={getStatusBadgeVariant(slot.appointment.status as AppointmentStatus)}
                    className="text-xs"
                  >
                    {getStatusTextBR(slot.appointment.status as AppointmentStatus)}
                  </Badge>
                )}
              </div>
              <p className="text-xs text-muted-foreground truncate mt-1">
                {getSlotTitle()}
              </p>
              {(slot.type === 'appointment' || slot.type === 'blocked') && (
                <div className="text-xs text-muted-foreground truncate">
                  {getSlotSubtitle()}
                </div>
              )}
            </div>
          </div>
          
          {/* Action buttons - responsive layout */}
          <div className="flex flex-row justify-end space-x-1 sm:flex-col sm:space-x-0 sm:space-y-1 sm:ml-2">
            {/* Appointment status actions */}
            {getAppointmentActions()}
            
            {/* Block/Unblock actions for available and blocked slots */}
            {slot.type === 'available' && canBlockSlots && (
              <Button
                size="sm"
                variant="outline"
                className="h-6 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={handleBlockClick}
                title="Bloquear horário"
              >
                <Lock className="h-3 w-3" />
              </Button>
            )}
            
            {slot.type === 'blocked' && canBlockSlots && slot.block && onUnblockSlot && (
              <Button
                size="sm"
                variant="outline"
                className="h-6 px-2 text-xs text-green-600 hover:text-green-700 hover:bg-green-50"
                onClick={handleUnblockClick}
                title="Desbloquear horário"
              >
                <Lock className="h-3 w-3" />
              </Button>
            )}
            
            {slot.type === 'available' && (
              <div className="text-xs text-center text-green-600 font-medium">
                Livre
              </div>
            )}
          </div>
        </div>

        {/* Additional info for appointments */}
        {slot.type === 'appointment' && slot.appointment && slot.appointment.healthcare_professional_name && (
          <div className="mt-2 pt-2 border-t border-blue-200">
            <p className="text-xs text-blue-600 truncate">
              Dr(a). {slot.appointment.healthcare_professional_name}
            </p>
          </div>
        )}

        {/* Special indicator for in-progress and completed appointments */}
        {slot.type === 'appointment' && slot.appointment && 
         ['in_progress', 'completed'].includes(slot.appointment.status) && canStartConsultation && (
          <div className="mt-2 pt-2 border-t border-purple-200">
            <p className="text-xs text-purple-600 text-center font-medium">
              {slot.appointment.status === 'in_progress' ? 
                '🩺 Clique para acessar o prontuário' : 
                '📋 Clique para ver o prontuário'
              }
            </p>
          </div>
        )}

        {/* Click hint for available slots */}
        {slot.type === 'available' && (
          <div className="mt-2 pt-2 border-t border-green-200">
            <p className="text-xs text-green-600 text-center">
              Clique para agendar consulta
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TimeSlotCard;
